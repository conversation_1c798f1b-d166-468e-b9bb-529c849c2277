<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup Label="Globals">
    <SccProjectName>SAK</SccProjectName>
    <SccProvider>SAK</SccProvider>
    <SccAuxPath>SAK</SccAuxPath>
    <SccLocalPath>SAK</SccLocalPath>
  </PropertyGroup>

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>enable</Nullable>
  </PropertyGroup>
	
  <ItemGroup>
    <ProjectReference Include="..\HdProject.Common\HdProject.Common.csproj" />
    <ProjectReference Include="..\HdProject.Core\HdProject.Core.csproj" />
    <ProjectReference Include="..\HdProject.Domain\HdProject.Domain.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Services\Interfaces\OperateData\" />
    <Folder Include="Services\OperateData\" />
    <Folder Include="Services\SaasPos\Booking\" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AutoMapper" Version="14.0.0" />
    <PackageReference Include="Furion" Version="4.9.7.42" />
    <PackageReference Include="LinqKit" Version="1.3.8" />
    <PackageReference Include="Microsoft.Extensions.Options" Version="9.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.*" />
    <PackageReference Include="SixLabors.ImageSharp" Version="3.1.8" />
    <PackageReference Include="StackExchange.Redis" Version="2.8.37" />
  <PackageReference Include="ClosedXML" Version="0.102.2" />
  </ItemGroup>

</Project>
