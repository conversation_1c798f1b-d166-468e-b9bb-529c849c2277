# Visual Studio generated files
.vs/
*.suo
*.user
*.userosscache
*.sln.docstates

# Build folders
**/[Bb]in/
**/[Oo]bj/

# Auto-generated files
*.Generated.cs
*.g.cs
*.g.i.cs
*.AssemblyAttributes.cs

# NuGet packages
*.nupkg
# The packages folder can be ignored because of PackageReference
**/packages/

# NuGet cache
*.nuget.props
*.nuget.targets
project.assets.json
project.nuget.cache

# Rider
.idea/

# Resharper
_ReSharper*/
*.DotSettings.user

# ASP.NET Scaffolding
ScaffoldingReadMe.txt

# Publish output
**/publish/

# IIS Express
/.vs/**/applicationhost.config

# User secrets
secrets.json

# Log files
*.log

# Visual Studio profiler
*.psess
*.vsp
*.vspx
*.sap

# Visual Studio Code
.vscode/

# Docker
**/docker-compose.*.yml
**/Dockerfile*

# Windows image file caches
Thumbs.db
ehthumbs.db
Desktop.ini

# macOS
.DS_Store

# JetBrains Rider log
.idea/**/workspace.xml
TestResults

HdProject.Tests