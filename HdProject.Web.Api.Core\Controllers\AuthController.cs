using HdProject.Application.Services.Interfaces;
using HdProject.Common.DTOs;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using HdProject.Web.Core;
using HdProject.Common.Logging;
using System.Diagnostics;
using HdProject.Domain.WebApi;

namespace HdProject.Web.Api.Core.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    public class AuthController : ControllerApiBase
    {
        private readonly IUserService _userService;

        public AuthController(IUserService userService)
        {
            _userService = userService;
        }

        [HttpPost("Login")]
        [AllowAnonymous]
        public async Task<IActionResult> Login([FromBody] UserLoginDto loginDto)
        {
            try
            {

                if (loginDto == null)
                {
                    return ApiError("无效的登录请求");
                }

                var loginResult = await _userService.LoginAsync(loginDto);

                if (!loginResult.IsSuccess)
                {

                    // 返回登录失败信息，使用401状态码表示认证失败
                    return ApiError("登录失败: " + loginResult.ErrorMessage, 401);
                }


                // 登录成功，返回Token
                return ApiData(loginResult.Token, "登录成功");
            }
            catch (Exception ex)
            {
                return ApiError("登录失败: " + ex.Message, 500);
            }
        }

        [HttpGet("GetUserId")]
        public string GetUserIdString()
        {
            return User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier)?.Value ?? "";
        }

        [HttpPost("ChangePassword")]
        [Authorize]
        public async Task<IActionResult> ChangePassword([FromBody] ChangePasswordDto changePasswordDto)
        {
            try
            {
                if (changePasswordDto == null || string.IsNullOrEmpty(changePasswordDto.OldPassword) || string.IsNullOrEmpty(changePasswordDto.NewPassword))
                {
                    return ApiError("无效的更改密码请求");
                }

                int userId = GetUserId();
                if (userId <= 0)
                {
                    return ApiError("无法获取用户信息", (int)ResultType.unauthorized);
                }

                bool result = await _userService.ChangePasswordAsync(userId, changePasswordDto.OldPassword, changePasswordDto.NewPassword);

                if (!result)
                {
                    return ApiError("更改密码失败：旧密码不正确");
                }

                return ApiSuccess("密码更改成功，请重新登录");
            }
            catch (Exception ex)
            {
                return ApiError("更改密码失败: " + ex.Message, (int)ResultType.internalServerError);
            }
        }

        [HttpPost("RefreshToken")]
        [AllowAnonymous]
        public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenDto refreshTokenDto)
        {
            try
            {
                if (refreshTokenDto == null || string.IsNullOrEmpty(refreshTokenDto.RefreshToken))
                {
                    return ApiError("无效的刷新令牌请求");
                }

                var result = await _userService.RefreshTokenAsync(refreshTokenDto.RefreshToken);

                if (!result.IsSuccess)
                {
                    return ApiError("刷新令牌失败: " + result.ErrorMessage, 401);
                }

                return ApiData(result.Token, "刷新令牌成功");
            }
            catch (Exception ex)
            {
                return ApiError("刷新令牌失败: " + ex.Message, 500);
            }
        }
    }
}