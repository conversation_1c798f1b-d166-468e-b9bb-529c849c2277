# 功能需求模板与示例

为了让AI能最高效、最准确地遵循 `DEVELOPMENT_WORKFLOW.md` 文档为您开发新功能，您可以按照本模板来提出需求。

---

### 需求描述模板

**1. 功能概述 (High-Level Goal)**
*   **一句话描述**: 您希望实现什么功能？

**2. 核心信息 (Core Scaffolding Info)**
*   **业务模块 (`<scope>`)**: 这个功能属于哪个大的业务领域？
*   **功能/实体名称 (`{Entity}`)**: 这个功能的核心实体是什么？

**3. 接口和功能点 (Endpoints & Actions)**
*   **需要哪些接口**:
    *   [ ] `Add` (新增)
    *   [ ] `Update` (更新)
    *   [ ] `Delete` (删除)
    *   [ ] `GetAll` (查询列表，分页)
    *   [ ] `GetById` (根据ID查询单个)
    *   [ ] **其他自定义接口**: (请列出)
*   **访问权限**: 接口是需要登录后访问，还是可以公开访问？

**4. 数据结构 (DTOs / Data Schema)**
*   **新增DTO (`{Entity}CreateDto`) 的字段**:
    *   `字段名`, `数据类型`, `备注/验证规则`
*   **更新DTO (`{Entity}UpdateDto`) 的字段**:
    *   `字段名`, `数据类型`, `备注/验证规则`
*   **查询DTO (`{Entity}QueryDto`) 的字段**:
    *   `字段名`, `数据类型`, `备注`

**5. 数据库信息 (Database Info)**
*   **对应数据表名**: (如果已经存在或已规划好)
*   **使用的数据仓库**: (如果不是默认的 `IRepositoryOperateData`)

**6. Git提交信息 (Git Commit Message)**
*   **Commit 类型 (`<type>`)**: `feat`, `fix`, `refactor` 等。
*   **Commit 主题 (`<subject>`)**: 简短描述。

---

### 完整需求示例

**功能需求：商品管理**

1.  **功能概述**: 创建一个商品管理模块，支持基础的增删改查。
2.  **核心信息**:
    *   **业务模块**: `SaasPos`
    *   **实体名称**: `Product`
3.  **接口和功能点**:
    *   需要 `Add`, `Update`, `Delete`, `GetAll`, `GetById` 标准接口。
    *   **访问权限**: 全部需要认证。
4.  **数据结构**:
    *   **CreateDto**:
        *   `Name` (string, 必填, MaxLength:100)
        *   `Price` (decimal, 必填)
        *   `CategoryId` (int, 必填)
    *   **UpdateDto**:
        *   `Id` (int, 必填)
        *   `Name` (string, 必填, MaxLength:100)
        *   `Price` (decimal, 必填)
        *   `CategoryId` (int, 必填)
    *   **QueryDto**:
        *   `Keyword` (string, 可选, 按Name查询)
        *   `CategoryId` (int?, 可选, 按分类ID查询)
5.  **数据库信息**:
    *   **数据表**: `T_Product`
    *   **数据仓库**: 使用默认的 `IRepositoryOperateData`
6.  **Git提交信息**:
    *   **Type**: `feat`
    *   **Scope**: `saaspos`
    *   **Subject**: `添加商品管理基础CRUD功能`
