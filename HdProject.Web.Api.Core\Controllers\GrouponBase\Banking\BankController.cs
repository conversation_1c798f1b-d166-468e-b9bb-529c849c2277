using HdProject.Application.Services.Interfaces.GrouponBase.Banking;
using HdProject.Common.DTOs;
using HdProject.Common.DTOs.Banking;
using HdProject.Domain.Entities.GroupBase;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.GrouponBase.Banking
{
    [ApiController]
    [Route("api/Banks")]
    [AllowAnonymous]
    public class BankController : ControllerBase
    {
        private readonly IBankService _service;
        public BankController(IBankService service)
        {
            _service = service;
        }

        [HttpGet]
        public async Task<ResponseContext<object>> Get([FromQuery] BankQueryDto query)
        {
            var list = await _service.GetListAsync(query);
            var total = await _service.CountAsync(query.Keyword);
            return new ResponseContext<object> { code = 200, data = new { rows = list, total } };
        }

        [HttpGet("{id:int}")]
        public async Task<ResponseContext<Dim_Bank?>> GetById(int id)
        {
            var data = await _service.GetByIdAsync(id);
            return new ResponseContext<Dim_Bank?> { code = 200, data = data };
        }

        [HttpPost]
        public async Task<ResponseContext<int>> Create([FromBody] BankCreateDto dto)
        {
            if (!ModelState.IsValid)
                return new ResponseContext<int> { code = 400, state = ResponseType.error, message = "参数验证失败" };
            var rows = await _service.CreateAsync(dto);
            return new ResponseContext<int> { code = 200, data = rows };
        }

        [HttpPut("{id:int}")]
        public async Task<ResponseContext<int>> Update(int id, [FromBody] BankUpdateDto dto)
        {
            if (!ModelState.IsValid)
                return new ResponseContext<int> { code = 400, state = ResponseType.error, message = "参数验证失败" };
            var rows = await _service.UpdateAsync(id, dto);
            return new ResponseContext<int> { code = 200, data = rows };
        }

        [HttpDelete("{id:int}")]
        public async Task<ResponseContext<int>> Delete(int id)
        {
            var rows = await _service.DeleteAsync(id);
            return new ResponseContext<int> { code = 200, data = rows };
        }
    }
}
