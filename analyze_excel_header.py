#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import openpyxl
from openpyxl import load_workbook
import json

def analyze_excel_structure(file_path):
    """分析Excel文件的表头结构"""
    try:
        # 使用openpyxl读取Excel文件以保持格式
        wb = load_workbook(file_path, data_only=False)
        ws = wb.active
        
        print(f"工作表名称: {ws.title}")
        print(f"最大行数: {ws.max_row}")
        print(f"最大列数: {ws.max_column}")
        print("\n" + "="*80)
        
        # 分析前10行的内容，这通常包含表头信息
        print("前10行内容分析:")
        for row_idx in range(1, min(11, ws.max_row + 1)):
            print(f"\n第{row_idx}行:")
            row_data = []
            for col_idx in range(1, min(21, ws.max_column + 1)):  # 只显示前20列
                cell = ws.cell(row=row_idx, column=col_idx)
                value = cell.value
                if value is not None:
                    row_data.append(f"列{col_idx}: {value}")
            
            if row_data:
                print("  " + " | ".join(row_data))
            else:
                print("  (空行)")
        
        print("\n" + "="*80)
        
        # 分析合并单元格
        print("合并单元格信息:")
        if ws.merged_cells:
            for merged_range in ws.merged_cells:
                print(f"  {merged_range}")
                # 获取合并单元格的值
                start_cell = ws.cell(row=merged_range.min_row, column=merged_range.min_col)
                print(f"    值: {start_cell.value}")
        else:
            print("  无合并单元格")
            
        print("\n" + "="*80)
        
        # 尝试使用pandas读取，看看数据结构
        print("使用pandas读取前5行数据:")
        try:
            df = pd.read_excel(file_path, header=None, nrows=5)
            print(df.to_string())
        except Exception as e:
            print(f"pandas读取失败: {e}")
            
        return True
        
    except Exception as e:
        print(f"分析Excel文件失败: {e}")
        return False

def analyze_json_data(json_data):
    """分析JSON数据结构"""
    print("\n" + "="*80)
    print("JSON数据结构分析:")
    
    if isinstance(json_data, str):
        try:
            data = json.loads(json_data)
        except:
            print("无法解析JSON字符串")
            return
    else:
        data = json_data
    
    if 'ObjceData' in data and data['ObjceData']:
        sample_record = data['ObjceData'][0]
        print(f"数据记录数: {len(data['ObjceData'])}")
        print("\n字段列表:")
        
        # 分类字段
        basic_fields = []
        revenue_fields = []
        batch_fields = []
        guest_fields = []
        timeslot_fields = []
        other_fields = []
        
        for key, value in sample_record.items():
            if any(x in key for x in ['日期', '门店', '星期']):
                basic_fields.append((key, value, type(value).__name__))
            elif '营收' in key or '收入' in key or '金额' in key:
                revenue_fields.append((key, value, type(value).__name__))
            elif '批' in key:
                batch_fields.append((key, value, type(value).__name__))
            elif '人数' in key:
                guest_fields.append((key, value, type(value).__name__))
            elif ':' in key and '-' in key:
                timeslot_fields.append((key, value, type(value).__name__))
            else:
                other_fields.append((key, value, type(value).__name__))
        
        print("\n基础信息字段:")
        for field, value, dtype in basic_fields:
            print(f"  {field}: {value} ({dtype})")
            
        print("\n营收字段:")
        for field, value, dtype in revenue_fields:
            print(f"  {field}: {value} ({dtype})")
            
        print("\n批次字段:")
        for field, value, dtype in batch_fields:
            print(f"  {field}: {value} ({dtype})")
            
        print("\n人数字段:")
        for field, value, dtype in guest_fields:
            print(f"  {field}: {value} ({dtype})")
            
        print("\n时间段字段:")
        timeslot_groups = {}
        for field, value, dtype in timeslot_fields:
            if '_' in field:
                timeslot, subfield = field.split('_', 1)
                if timeslot not in timeslot_groups:
                    timeslot_groups[timeslot] = []
                timeslot_groups[timeslot].append((subfield, value, dtype))
        
        for timeslot, subfields in timeslot_groups.items():
            print(f"  {timeslot}:")
            for subfield, value, dtype in subfields:
                print(f"    {subfield}: {value} ({dtype})")
                
        print("\n其他字段:")
        for field, value, dtype in other_fields:
            print(f"  {field}: {value} ({dtype})")

if __name__ == "__main__":
    # 分析Excel文件
    excel_file = "docs/【名堂旗舰店】每日业绩报表2025年7月.xlsx"
    print("分析Excel文件结构...")
    analyze_excel_structure(excel_file)
    
    # 分析示例JSON数据
    sample_json = '''{"list":[],"ObjceData":[{"日期":"\/Date(1755360000000)\/","门店":"名堂旗舰店","星期":"星期日","营收_总收入":81496.00,"营收_白天档":51949.00,"营收_晚上档":29547.00,"全天总批数":97,"白天档_总批次":84,"晚上档_总批次":13,"白天档_直落":3,"晚上档_直落":3,"自助餐人数":341,"直落人数":51,"11:50-14:50_K+":5,"11:50-14:50_特权预约":3,"11:50-14:50_美团":15,"11:50-14:50_抖音":3,"11:50-14:50_房费":0,"11:50-14:50_小计":26,"11:50-14:50_上档直落":0,"13:30-16:30_K+":10,"13:30-16:30_特权预约":4,"13:30-16:30_美团":4,"13:30-16:30_抖音":2,"13:30-16:30_房费":0,"13:30-16:30_小计":20,"13:30-16:30_上档直落":1,"15:00-18:00_K+":7,"15:00-18:00_特权预约":2,"15:00-18:00_美团":3,"15:00-18:00_抖音":0,"15:00-18:00_房费":0,"15:00-18:00_小计":12,"15:00-18:00_上档直落":3,"17:00-20:00_K+":4,"17:00-20:00_特权预约":0,"17:00-20:00_美团":5,"17:00-20:00_抖音":2,"17:00-20:00_房费":0,"17:00-20:00_小计":11,"17:00-20:00_上档直落":3,"18:00-21:00_K+":0,"18:00-21:00_特权预约":0,"18:00-21:00_美团":0,"18:00-21:00_抖音":0,"18:00-21:00_房费":0,"18:00-21:00_小计":0,"18:00-21:00_上档直落":0,"18:10-21:10_K+":2,"18:10-21:10_特权预约":0,"18:10-21:10_美团":6,"18:10-21:10_抖音":3,"18:10-21:10_房费":0,"18:10-21:10_小计":11,"18:10-21:10_上档直落":6,"19:00-22:00_K+":2,"19:00-22:00_特权预约":1,"19:00-22:00_美团":1,"19:00-22:00_抖音":0,"19:00-22:00_房费":0,"19:00-22:00_小计":4,"19:00-22:00_上档直落":7,"19:00-21:30_K+":0,"19:00-21:30_特权预约":0,"19:00-21:30_美团":0,"19:00-21:30_抖音":0,"19:00-21:30_房费":0,"19:00-21:30_小计":0,"19:00-21:30_上档直落":0,"k+餐批次":84,"k+餐直落批数":3,"17点 18点 19点档直落":3,"k+自由餐_k+":0,"k+自由餐_特权预约":0,"k+自由餐_美团":0,"k+自由餐_抖音":0,"k+自由餐_小计":0,"k+自由餐_营业额":0.00,"20点后_买断批次":2,"20点后_畅饮套餐":2,"20点后_自由消套餐":1,"20点后_促销套餐_特权预约":0,"20点后_促销套餐_美团":0,"20点后_促销套餐_抖音":0,"20点后房费批次":"8","20点后其他批次":0,"20点后_批次小计":13,"20点后_营收金额":23460,"招待批次":0,"招待金额":0.00}],"errorCode":null,"success":true,"total":0,"PagCount":0,"Msg":null,"GrouponGiveName":null}'''
    
    analyze_json_data(sample_json)
