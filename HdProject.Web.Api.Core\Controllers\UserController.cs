using System.Security.Claims;
using HdProject.Application.Services.Interfaces;
using Microsoft.AspNetCore.Authorization;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers
{
    public class UserController : ControllerApiBase
    {
        private readonly IUserService _userService;

        public UserController(IUserService userService)
        {
            _userService = userService;
        }

        [HttpGet("GetProfile")]
        public async Task<IActionResult> GetUserProfile()
        {
            try
            {
                // 从token中获取用户ID
                var userIdClaim = User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim == null || !int.TryParse(userIdClaim.Value, out int userId))
                {
                    return Unauthorized(new { message = "无效的用户信息" });
                }

                var userDto = await _userService.GetUserByIdAsync(userId);
                if (userDto == null)
                {
                    return NotFound(new { message = "用户不存在" });
                }

                return Ok(userDto);
            }
            catch (Exception ex)
            {
                return StatusCode(500, new { message = "获取用户信息失败", error = ex.Message });
            }
        }

        [HttpGet("AdminOnly")]
        [Authorize(Policy = "RequireAdminRole")] // 只有管理员才能访问
        public IActionResult AdminOnlyEndpoint()
        {
            return Ok(new { message = "你拥有管理员权限!" });
        }

        [HttpGet("Test")]
        public int Get()
        {
            return int.Parse("aaaa");
        }
    }
}
