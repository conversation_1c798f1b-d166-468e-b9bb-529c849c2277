using System.Threading.Tasks;
using HdProject.Domain.Context.Reports;

namespace HdProject.Application.Services.Interfaces.Reports
{
    public interface IReportService
    {
        Task<BankSummaryResponseDto> GenerateBankSummaryReportAsync(BankSummaryRequestDto request);
        // 导出银行汇总报表为 XLSX（叠表头）
        Task<(byte[] Bytes, string FileName, string ContentType)> ExportBankSummaryXlsxAsync(BankSummaryRequestDto request, string? titleDateRange = null);
    }
}
