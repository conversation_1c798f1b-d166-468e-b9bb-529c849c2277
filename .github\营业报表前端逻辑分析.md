# 营业报表前端逻辑分析文档

## 概述

本文档详细分析Vue3管理平台中营业报表相关的前端代码逻辑，为后端开发人员实现Excel导出功能提供技术参考。

## 1. 报表类型与功能概览

### 1.1 主要报表类型
- **营业报表** (`BillingReport`): 每日营业收入统计
- **预定报表** (`BookingReport`): 预定房间数据统计  
- **新旧客对比报表** (`OldNewReport`): 客户消费行为分析
- **大学生优惠报表** (`StudentsReport`): 学生优惠统计
- **银行券管理** (`BankCouponReport`): 银行券交易数据

### 1.2 核心功能
- 多门店数据查询
- 时间范围筛选
- 动态时间段数据展示
- CSV/Excel导出
- 数据格式化与计算

## 2. 营业报表详细分析

### 2.1 API接口规范

#### 2.1.1 数据查询接口
```javascript
// 营业报表数据获取
GET http://*************:88/ExecUse/index
参数:
- Ex: "OperateData.dbo.usp_GenerateDynamicUnifiedDailyReport"
- ShopId: 门店ID (数字)
- BeginDate: 开始日期 (YYYYMMDD格式)
- EndDate: 结束日期 (YYYYMMDD格式)

// 预定报表数据获取  
GET http://*************:88/ExecUse/index
参数:
- Ex: "OperateData.dbo.usp_GetBookingReport"
- ShopId: 门店ID
- BeginDate: 开始日期
- EndDate: 结束日期
- lang: "en" | "zh"
```

#### 2.1.2 导出接口
```javascript
// CSV导出接口
GET http://*************:88/ExportToCsv/export
参数:
- Ex: 存储过程名称
- ShopId: 门店ID
- BeginDate: 开始日期
- EndDate: 结束日期
- lang: "zh" | "en"
响应类型: blob (文件流)
```

#### 2.1.3 门店信息接口
```javascript
// 获取门店列表
GET http://*************:200/ExecUse/Index
参数:
- Ex: "GrouponBase.dbo.Ex_SelTable"
- TableName: "Mims.dbo.ShopInfo"
```

### 2.2 数据结构定义

#### 2.2.1 营业报表核心字段
```typescript
interface BillingReportData {
  // 基础信息
  ShopName: string;           // 门店名称
  ReportDate: string;         // 报表日期 (/Date(timestamp)/ 格式)
  Weekday: string;            // 星期

  // 营收数据
  Revenue_Total: number;      // 总营业收入
  Revenue_DayTime: number;    // 白天档收入
  Revenue_NightTime: number;  // 晚档收入

  // 带客数据
  TotalBatchCount_AllDay: number;              // 全天总营客批数
  DayTime_KPlusMeal_BatchCount: number;        // 白天K+餐批数
  DayTime_DirectFallBatch: number;             // 白天直落批数
  NightTime_TotalBatch: number;                // 20点后进场批数
  NightTime_DirectFallBatch: number;           // 晚上直落批数

  // 用餐人数
  BuffetGuestCount: number;                    // K+餐人数
  TotalDirectFallGuests: number;               // 直落人数

  // 动态时间段数据 (格式: "HH:MM-HH:MM_字段名")
  [timeSlotField: string]: any;
}
```

#### 2.2.2 时间段动态字段
```typescript
// 时间段字段命名规则: "开始时间-结束时间_字段名"
// 例如: "11:30-12:30_KPlus", "17:00-18:00_Meituan"

interface TimeSlotFields {
  KPlus: number;                    // K+人数
  SpecialReservation: number;       // 特权预约人数
  Meituan: number;                  // 美团人数
  Douyin: number;                   // 抖音人数
  RoomFee: number;                  // 房费
  PrevSlotDirectFall: number;       // 上一档直落
  Subtotal: number;                 // 小计
}
```

#### 2.2.3 预定报表字段
```typescript
interface BookingReportData {
  ShopName: string;                 // 门店名称
  ReportDate: string;               // 报表日期
  Weekday: string;                  // 星期
  TotalRevenue: number;             // 当天总营业额
  
  // 动态时间段字段 (格式: "HH:MM_字段名" 或 "HH:MM-HH:MM_字段名")
  [timeSlotField: string]: any;
}

interface BookingTimeSlotFields {
  BookedRooms: number;              // 预定房间数
  BookedGuests: number;             // 预定人数
}
```

### 2.3 数据处理逻辑

#### 2.3.1 时间段数据解析
```javascript
// 营业报表时间段正则匹配
const timeMatch = key.match(/^(\d{2}:\d{2}-\d{2}:\d{2})_(.+)$/);

// 预定报表时间段正则匹配 (支持开放式结束时间)
const timeMatch = key.match(/^(\d{2}:\d{2})(?:-(\d{2}:\d{2}))?_(.+)$/);

// 时间段处理逻辑
if (timeMatch) {
  const [_, startTime, endTime, field] = timeMatch;
  const timeSlot = endTime !== undefined 
    ? `${startTime}-${endTime}` 
    : `${startTime}-END`;
}
```

#### 2.3.2 数据格式化规则

##### 日期格式化
```javascript
const formatDate = (row, column, cellValue) => {
  if (column.property === "ReportDate") {
    // 处理 /Date(1753286400000)/ 格式
    const timestamp = parseInt(cellValue.match(/\d+/)[0]);
    return new Date(timestamp).toLocaleDateString();
  }
  return cellValue;
};
```

##### 数值格式化
- 金额字段: 保持原始数值，前端显示时可添加千分位分隔符
- 人数/批数: 整数显示，0值显示为0
- 小数位数: 根据字段类型确定，一般金额保留2位小数

### 2.4 表格列配置

#### 2.4.1 营业报表列结构
```javascript
// 基础信息列
{
  prop: "ReportDate",
  label: "日期", 
  width: "100",
  formatter: formatDate
}

// 营收数据列组
{
  label: "每日营收数据",
  children: [
    { prop: "Revenue_Total", label: "总营业收入", width: "100" },
    { prop: "Revenue_DayTime", label: "白天档", width: "100" },
    { prop: "Revenue_NightTime", label: "晚档", width: "100" }
  ]
}

// 动态时间段列 (根据数据动态生成)
timeSlotsData.timeSlots.map(slot => ({
  label: slot.timeSlot,
  children: [
    { prop: "KPlus", label: "K+", width: "80" },
    { prop: "SpecialReservation", label: "特权预约", width: "80" },
    // ... 其他子列
  ]
}))
```

## 3. 查询参数与筛选条件

### 3.1 门店筛选
```javascript
// 门店数据结构
interface StoreOption {
  value: number;        // ShopID
  label: string;        // ShopName
}

// 门店过滤条件
const validStores = stores.filter(shop => 
  shop.IsUse && 
  shop.ShopName !== "清远店" && 
  shop.ShopName !== "黄岐店"
);
```

### 3.2 时间范围筛选
```javascript
// 日期格式: YYYYMMDD
// 默认查询前一天数据
const getTodayRange = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate() - 1).padStart(2, "0");
  const todayStr = `${year}${month}${day}`;
  return [todayStr, todayStr];
};

// 日期限制: 不能选择未来日期
const disabledDate = time => {
  const now = new Date();
  now.setDate(now.getDate() - 1);
  return time > now;
};
```

## 4. 导出功能实现

### 4.1 文件命名规则
```javascript
// 营业报表文件名格式
const fileName = `[${selectedOption.label}]每日营业报表_${beginDate}-${endDate}.csv`;

// 预定报表文件名格式  
const fileName = `[${selectedOption.label}]预定报表_${beginDate}-${endDate}.csv`;
```

### 4.2 导出流程
1. 参数验证 (门店ID、时间范围)
2. 调用导出API获取blob数据
3. 创建下载链接并触发下载
4. 清理临时对象

```javascript
const exportData = async () => {
  const response = await axios.get(exportUrl, {
    params: exportParams,
    responseType: "blob",
    timeout: 5000
  });
  
  const blob = new Blob([response.data]);
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(link.href);
};
```

## 5. 错误处理与用户体验

### 5.1 数据验证
- 门店选择验证
- 时间范围完整性检查
- 空数据提示

### 5.2 加载状态管理
```javascript
const tableLoad = ref(false);      // 表格加载状态
const isExporting = ref(false);    // 导出加载状态
```

### 5.3 错误提示
```javascript
// 参数验证失败
ElMessage.error("请选择门店和完整的时间范围！");

// 无数据提示
ElMessage("当前选择的日期没有查询到数据！");

// 导出失败提示
ElMessage.error(`导出失败: ${error.message}`);
```

## 6. 后端Excel导出建议

### 6.1 数据结构保持一致
- 保持与前端相同的字段命名
- 维护时间段动态字段的命名规则
- 确保数据类型匹配

### 6.2 Excel格式要求
- 表头分组结构与前端表格一致
- 日期格式: YYYY-MM-DD
- 数值格式: 金额保留2位小数，整数类型不显示小数
- 支持中文列名

### 6.3 性能优化
- 大数据量时考虑分页或流式处理
- 缓存门店信息减少重复查询
- 异步处理导出任务

## 7. 技术要点总结

1. **动态列生成**: 基于时间段数据动态创建表格列
2. **数据重组**: 将平铺的时间段字段重组为嵌套结构
3. **格式化处理**: 日期、数值的统一格式化
4. **文件下载**: Blob方式处理文件流下载
5. **状态管理**: 加载状态和错误处理的用户体验优化

此文档为后端开发Excel导出功能提供了完整的前端逻辑参考，确保导出数据与前端显示保持一致。

## 8. 详细字段映射表

### 8.1 营业报表完整字段清单

#### 基础信息字段
| 前端字段名 | 中文名称 | 数据类型 | 格式说明 |
|-----------|---------|---------|----------|
| ShopName | 门店名称 | string | 门店中文名称 |
| ReportDate | 日期 | string | /Date(timestamp)/格式，需转换为YYYY-MM-DD |
| Weekday | 星期 | string | 中文星期显示 |

#### 营收数据字段
| 前端字段名 | 中文名称 | 数据类型 | 格式说明 |
|-----------|---------|---------|----------|
| Revenue_Total | 总营业收入 | number | 金额，保留2位小数 |
| Revenue_DayTime | 白天档 | number | 金额，保留2位小数 |
| Revenue_NightTime | 晚档 | number | 金额，保留2位小数 |

#### 带客数据字段
| 前端字段名 | 中文名称 | 数据类型 | 格式说明 |
|-----------|---------|---------|----------|
| TotalBatchCount_AllDay | 全天总营客批数 | number | 整数 |
| DayTime_KPlusMeal_BatchCount | K+餐批数 | number | 整数 |
| DayTime_DirectFallBatch | 白天直落批数 | number | 整数 |
| DayTime_KPlusMeal_DirectFallBatch | K+餐直落批数 | number | 整数 |
| NightTime_TotalBatch | 20点后进场 | number | 整数 |
| NightTime_DirectFallBatch | 晚上直落批数 | number | 整数 |
| NightTime_EarlySlots_DirectFallBatch | 17点档18点档19点档直落 | number | 整数 |

#### 用餐人数字段
| 前端字段名 | 中文名称 | 数据类型 | 格式说明 |
|-----------|---------|---------|----------|
| BuffetGuestCount | K+餐人数 | number | 整数 |
| TotalDirectFallGuests | 直落人数 | number | 整数 |

#### K+自由餐字段 (20:00-END时间段)
| 前端字段名 | 中文名称 | 数据类型 | 格式说明 |
|-----------|---------|---------|----------|
| Night_FreeMeal_KPlus | K+ | number | 整数 |
| Night_FreeMeal_SpecialReservation | 特权预约 | number | 整数 |
| Night_FreeMeal_Meituan | 美团 | number | 整数 |
| Night_FreeMeal_Douyin | 抖音 | number | 整数 |
| Night_FreeMeal_SubtotalBatch | 小计 | number | 整数 |
| Night_FreeMeal_Revenue | K+自由餐消费金额 | number | 金额，保留2位小数 |

#### 20点后进场字段
| 前端字段名 | 中文名称 | 数据类型 | 格式说明 |
|-----------|---------|---------|----------|
| After8PM_BuyoutBatch | 买断套餐 | number | 整数 |
| After8PM_AllYouCanDrinkPackageBatch | 畅饮套餐 | number | 整数 |
| After8PM_FreeConsumptionPackageBatch | 自由消套餐 | number | 整数 |
| After8PM_Promo_SpecialReservation | 促销特权 | number | 整数 |
| After8PM_Promo_Meituan | 促销美团 | number | 整数 |
| After8PM_Promo_Douyin | 促销抖音 | number | 整数 |
| After8PM_RoomFeeBatch | 房费 | number | 整数 |
| After8PM_OtherBatch | 其他 | number | 整数 |
| After8PM_SubtotalBatch | 批数小计 | number | 整数 |
| After8PM_Revenue | 20点后消费金额 | number | 金额，保留2位小数 |

#### 招待字段
| 前端字段名 | 中文名称 | 数据类型 | 格式说明 |
|-----------|---------|---------|----------|
| Complimentary_BatchCount | 招待批数 | number | 整数 |
| Complimentary_Revenue | 打折金额免单金额 | number | 金额，保留2位小数 |

### 8.2 预定报表字段清单

#### 基础信息字段
| 前端字段名 | 中文名称 | 数据类型 | 格式说明 |
|-----------|---------|---------|----------|
| ShopName | 门店 | string | 门店中文名称 |
| ReportDate | 日期 | string | /Date(timestamp)/格式，需转换为YYYY-MM-DD |
| Weekday | 星期 | string | 中文星期显示 |
| TotalRevenue | 当天总营业额 | number | 金额，保留2位小数 |

#### 动态时间段字段 (格式: "HH:MM-HH:MM_字段名")
| 字段后缀 | 中文名称 | 数据类型 | 格式说明 |
|---------|---------|---------|----------|
| _BookedRooms | 预定房间数 | number | 整数 |
| _BookedGuests | 预定人数 | number | 整数 |

### 8.3 新旧客对比报表字段清单

| 前端字段名 | 中文名称 | 数据类型 | 格式说明 |
|-----------|---------|---------|----------|
| workdate | 营业日期 | string | YYYY-MM-DD格式 |
| shopid | 店别 | number | 门店ID，需映射为门店名称 |
| invno | 账单号 | string | 账单编号 |
| fdcname | 下单名称 | string | 商品/服务名称 |
| fdqty | 下单数量 | number | 整数 |
| rmno | 房号 | string | 房间编号 |
| custtel | 开房手机号 | string | 手机号码 |
| Val4 | 会员卡号 | string | 会员卡编号 |
| memberphonenumber | 会员手机号 | string | 会员手机号码 |
| LatelyConsumeTime | 最后消费时间 | string | 日期时间格式 |

## 9. 数据处理算法详解

### 9.1 时间段数据重组算法

```javascript
// 核心算法：将平铺的时间段字段重组为嵌套结构
const processTimeSlotData = (rawData) => {
  const allSlots = {};
  const allBaseData = [];

  rawData.forEach(item => {
    const slots = {};
    const baseData = {};

    // 遍历每个字段，分离时间段数据和基础数据
    Object.keys(item).forEach(key => {
      // 营业报表时间段匹配: "HH:MM-HH:MM_字段名"
      const timeMatch = key.match(/^(\d{2}:\d{2}-\d{2}:\d{2})_(.+)$/);

      if (timeMatch) {
        const [_, timeSlot, field] = timeMatch;
        if (!slots[timeSlot]) {
          slots[timeSlot] = {};
        }
        slots[timeSlot][field] = item[key];
      } else {
        baseData[key] = item[key];
      }
    });

    allBaseData.push(baseData);

    // 合并时间段数据
    Object.keys(slots).forEach(timeSlot => {
      if (!allSlots[timeSlot]) {
        allSlots[timeSlot] = [];
      }
      allSlots[timeSlot].push(slots[timeSlot]);
    });
  });

  return {
    baseData: allBaseData,
    timeSlots: Object.keys(allSlots).map(timeSlot => ({
      timeSlot,
      data: allSlots[timeSlot]
    }))
  };
};
```

### 9.2 预定报表特殊处理

```javascript
// 预定报表支持开放式结束时间: "HH:MM_字段名" 或 "HH:MM-HH:MM_字段名"
const processBookingTimeSlot = (key) => {
  const timeMatch = key.match(/^(\d{2}:\d{2})(?:-(\d{2}:\d{2}))?_(.+)$/);

  if (timeMatch) {
    const [_, startTime, endTime, field] = timeMatch;
    const timeSlot = endTime !== undefined
      ? `${startTime}-${endTime}`
      : `${startTime}-END`;
    return { timeSlot, field };
  }
  return null;
};
```

## 10. Excel导出技术规范

### 10.1 Excel工作表结构

#### 营业报表工作表布局
```
A1: 门店名称 (合并单元格)
A2: 日期 | B2: 星期 | C2: 总营业收入 | D2: 白天档 | E2: 晚档 | ...

动态时间段列从F列开始，每个时间段包含多个子列：
F1: 11:30-12:30 (合并单元格)
F2: K+ | G2: 特权预约 | H2: 美团 | I2: 抖音 | J2: 房费 | K2: 上一档直落 | L2: 小计
```

#### 表头层级结构
```
第一层: 主分组 (门店名称、每日营收数据、每日带客数据、用餐人数、K+自助餐、K+自由餐、20点后进场、招待)
第二层: 子分组 (白天档、晚档、时间段等)
第三层: 具体字段 (日期、星期、各项数值)
```

### 10.2 数据验证规则

```javascript
// 必填字段验证
const requiredFields = ['ShopName', 'ReportDate', 'Weekday'];

// 数值字段验证
const numericFields = [
  'Revenue_Total', 'Revenue_DayTime', 'Revenue_NightTime',
  'TotalBatchCount_AllDay', 'BuffetGuestCount'
];

// 时间段字段验证
const timeSlotPattern = /^(\d{2}:\d{2}-\d{2}:\d{2})_(.+)$/;
```

### 10.3 格式化规则实现

```javascript
// 日期格式化
const formatReportDate = (dateString) => {
  if (dateString.includes('/Date(')) {
    const timestamp = parseInt(dateString.match(/\d+/)[0]);
    return new Date(timestamp).toLocaleDateString('zh-CN');
  }
  return dateString;
};

// 金额格式化
const formatCurrency = (amount) => {
  return typeof amount === 'number'
    ? amount.toFixed(2)
    : '0.00';
};

// 整数格式化
const formatInteger = (value) => {
  return typeof value === 'number'
    ? Math.round(value).toString()
    : '0';
};
```

## 11. 性能优化建议

### 11.1 前端性能优化
- 使用计算属性缓存时间段数据处理结果
- 虚拟滚动处理大量数据展示
- 防抖处理搜索请求

### 11.2 后端性能优化
- 数据库查询优化，添加适当索引
- 缓存门店信息和常用查询结果
- 分页处理大数据量导出
- 异步任务处理长时间导出操作

### 11.3 内存管理
- 及时清理Blob对象和临时链接
- 控制同时导出任务数量
- 合理设置请求超时时间

此文档提供了营业报表系统的完整技术规范，为后端Excel导出功能开发提供详细的参考依据。

## 12. 实用代码示例

### 12.1 通用工具函数

#### 12.1.1 日期格式化工具
```javascript
// 处理 .NET 日期格式 /Date(timestamp)/
const formatDotNetDate = (dateString) => {
  if (!dateString || !dateString.includes('/Date(')) {
    return dateString;
  }
  const timestamp = parseInt(dateString.match(/\d+/)[0]);
  return new Date(timestamp).toLocaleDateString('zh-CN');
};

// 获取日期范围（默认前一天）
const getDefaultDateRange = () => {
  const today = new Date();
  const year = today.getFullYear();
  const month = String(today.getMonth() + 1).padStart(2, "0");
  const day = String(today.getDate() - 1).padStart(2, "0");
  const dateStr = `${year}${month}${day}`;
  return [dateStr, dateStr];
};

// 日期选择器禁用未来日期
const disableFutureDate = (time) => {
  const now = new Date();
  now.setDate(now.getDate() - 1);
  return time > now;
};
```

#### 12.1.2 API响应处理工具
```javascript
// 处理带括号的JSON响应
const parseWrappedJson = (responseData) => {
  if (typeof responseData === "string" && responseData.startsWith("(")) {
    return JSON.parse(responseData.replace(/^\((.*)\)$/, "$1"));
  }
  return responseData;
};

// 统一API请求错误处理
const handleApiError = (error, operation = "操作") => {
  console.error(`${operation}失败:`, error);
  const message = error.response?.data?.message || error.message || `${operation}失败`;
  ElMessage.error(message);
  throw error;
};
```

#### 12.1.3 文件下载工具
```javascript
// 通用文件下载函数
const downloadFile = (blob, fileName) => {
  const link = document.createElement("a");
  link.href = URL.createObjectURL(blob);
  link.download = fileName;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(link.href);
};

// 生成报表文件名
const generateReportFileName = (reportType, storeName, startDate, endDate, extension = 'csv') => {
  return `[${storeName}]${reportType}_${startDate}-${endDate}.${extension}`;
};
```

### 12.2 数据验证函数

```javascript
// 参数验证
const validateReportParams = (storeId, timeRange) => {
  if (!storeId) {
    ElMessage.error("请选择门店！");
    return false;
  }
  if (!timeRange || timeRange.length !== 2) {
    ElMessage.error("请选择完整的时间范围！");
    return false;
  }
  return true;
};

// 数据完整性检查
const validateTableData = (data) => {
  if (!data || !Array.isArray(data) || data.length === 0) {
    ElMessage.warning("当前选择的日期没有查询到数据！");
    return false;
  }
  return true;
};
```

### 12.3 表格配置生成器

```javascript
// 动态生成时间段列配置
const generateTimeSlotColumns = (timeSlots, fieldConfigs) => {
  return timeSlots.map(slot => ({
    label: slot.timeSlot,
    align: "center",
    children: fieldConfigs.map(config => ({
      prop: config.prop,
      label: config.label,
      width: config.width || "80",
      align: "center",
      formatter: (row, column, cellValue, index) => {
        const slotData = timeSlots.find(s => s.timeSlot === slot.timeSlot);
        return slotData?.data[index]?.[config.prop] || 0;
      }
    }))
  }));
};

// 基础列配置
const baseColumns = [
  { prop: "ShopName", label: "门店", width: "100" },
  { prop: "ReportDate", label: "日期", width: "100", formatter: formatDotNetDate },
  { prop: "Weekday", label: "星期", width: "80" }
];
```

### 12.4 完整的报表组件示例

```javascript
// 营业报表组合式API示例
import { ref, computed, onMounted } from 'vue';
import { ElMessage } from 'element-plus';
import axios from 'axios';

export const useBillingReport = () => {
  // 响应式数据
  const tableData = ref([]);
  const tableLoad = ref(false);
  const isExporting = ref(false);
  const stores = ref([]);
  const selectedStore = ref('');
  const dateRange = ref(getDefaultDateRange());

  // 计算属性 - 时间段数据处理
  const timeSlotsData = computed(() => {
    if (!tableData.value.length) return { baseData: [], timeSlots: [] };

    const allSlots = {};
    const allBaseData = [];

    tableData.value.forEach(item => {
      const slots = {};
      const baseData = {};

      Object.keys(item).forEach(key => {
        const timeMatch = key.match(/^(\d{2}:\d{2}-\d{2}:\d{2})_(.+)$/);

        if (timeMatch) {
          const [_, timeSlot, field] = timeMatch;
          if (!slots[timeSlot]) slots[timeSlot] = {};
          slots[timeSlot][field] = item[key];
        } else {
          baseData[key] = item[key];
        }
      });

      allBaseData.push(baseData);

      Object.keys(slots).forEach(timeSlot => {
        if (!allSlots[timeSlot]) allSlots[timeSlot] = [];
        allSlots[timeSlot].push(slots[timeSlot]);
      });
    });

    return {
      baseData: allBaseData,
      timeSlots: Object.keys(allSlots).map(timeSlot => ({
        timeSlot,
        data: allSlots[timeSlot]
      }))
    };
  });

  // 方法
  const fetchStores = async () => {
    try {
      const response = await axios.get("http://*************:200/ExecUse/Index", {
        params: {
          Ex: "GrouponBase.dbo.Ex_SelTable",
          TableName: "Mims.dbo.ShopInfo"
        }
      });

      const data = parseWrappedJson(response.data);
      stores.value = data.ObjceData
        .filter(shop => shop.IsUse && !['清远店', '黄岐店'].includes(shop.ShopName))
        .map(shop => ({ value: shop.ShopID, label: shop.ShopName }));
    } catch (error) {
      handleApiError(error, "获取门店列表");
    }
  };

  const fetchReportData = async () => {
    if (!validateReportParams(selectedStore.value, dateRange.value)) return;

    try {
      tableLoad.value = true;
      const response = await axios.get("http://*************:88/ExecUse/index", {
        params: {
          Ex: "OperateData.dbo.usp_GenerateDynamicUnifiedDailyReport",
          ShopId: selectedStore.value,
          BeginDate: dateRange.value[0],
          EndDate: dateRange.value[1]
        },
        timeout: 5000
      });

      const data = parseWrappedJson(response.data);
      tableData.value = data.ObjceData || [];

      if (!validateTableData(tableData.value)) {
        tableData.value = [];
      }
    } catch (error) {
      handleApiError(error, "查询报表数据");
    } finally {
      tableLoad.value = false;
    }
  };

  const exportReport = async () => {
    if (!validateReportParams(selectedStore.value, dateRange.value)) return;

    try {
      isExporting.value = true;
      const response = await axios.get("http://*************:88/ExportToCsv/export", {
        params: {
          Ex: "OperateData.dbo.usp_GenerateDynamicUnifiedDailyReport",
          ShopId: selectedStore.value,
          BeginDate: dateRange.value[0],
          EndDate: dateRange.value[1],
          lang: "zh"
        },
        responseType: "blob",
        timeout: 10000
      });

      const selectedOption = stores.value.find(option => option.value === selectedStore.value);
      const fileName = generateReportFileName(
        "每日营业报表",
        selectedOption.label,
        dateRange.value[0],
        dateRange.value[1]
      );

      downloadFile(new Blob([response.data]), fileName);
      ElMessage.success("导出成功！");
    } catch (error) {
      handleApiError(error, "导出报表");
    } finally {
      isExporting.value = false;
    }
  };

  // 初始化
  onMounted(() => {
    fetchStores();
  });

  return {
    // 数据
    tableData,
    tableLoad,
    isExporting,
    stores,
    selectedStore,
    dateRange,
    timeSlotsData,

    // 方法
    fetchReportData,
    exportReport,

    // 工具函数
    formatDotNetDate,
    disableFutureDate
  };
};
```

## 13. 常见问题与解决方案

### 13.1 数据格式问题
**问题**: .NET日期格式 `/Date(timestamp)/` 无法正确显示
**解决**: 使用正则表达式提取时间戳并转换为标准日期格式

### 13.2 时间段数据处理
**问题**: 动态时间段字段无法正确解析
**解决**: 使用正则匹配 `^(\d{2}:\d{2}-\d{2}:\d{2})_(.+)$` 模式分离时间段和字段名

### 13.3 导出文件乱码
**问题**: CSV导出中文显示乱码
**解决**: 确保响应头设置正确的字符编码，前端使用UTF-8处理

### 13.4 大数据量性能
**问题**: 数据量大时页面卡顿
**解决**: 实现虚拟滚动、分页加载或数据懒加载

### 13.5 网络超时处理
**问题**: 导出大文件时请求超时
**解决**: 增加超时时间设置，实现重试机制和进度提示

---

**文档版本**: v1.0
**最后更新**: 2024年
**维护人员**: 前端开发团队

此技术文档为营业报表Excel导出功能提供了全面的开发指南，包含详细的字段映射、数据处理逻辑和实用代码示例。
