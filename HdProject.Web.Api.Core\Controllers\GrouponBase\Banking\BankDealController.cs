using HdProject.Application.Services.Interfaces.GrouponBase.Banking;
using HdProject.Common.DTOs;
using HdProject.Common.DTOs.Banking;
using HdProject.Domain.Entities.GroupBase;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.GrouponBase.Banking
{
    [ApiController]
    [Route("api/BankDeals")]
    [AllowAnonymous]
    public class BankDealController : ControllerBase
    {
        private readonly IBankDealService _service;
        public BankDealController(IBankDealService service)
        {
            _service = service;
        }

        [HttpGet]
        public async Task<ResponseContext<object>> Get([FromQuery] BankDealQueryDto query)
        {
            var list = await _service.GetListAsync(query);
            var total = await _service.CountAsync(query.BankSKs, query.FdNo, query.DealName);
            return new ResponseContext<object> { code = 200, data = new { rows = list, total } };
        }

        [HttpGet("{id:int}")]
        public async Task<ResponseContext<Dim_Bank_Deal?>> GetById(int id)
        {
            var data = await _service.GetByIdAsync(id);
            return new ResponseContext<Dim_Bank_Deal?> { code = 200, data = data };
        }

        [HttpPost]
        public async Task<ResponseContext<int>> Create([FromBody] BankDealCreateDto dto)
        {
            if (!ModelState.IsValid)
                return new ResponseContext<int> { code = 400, state = ResponseType.error, message = "参数验证失败" };
            var rows = await _service.CreateAsync(dto);
            return new ResponseContext<int> { code = 200, data = rows };
        }

        [HttpPut("{id:int}")]
        public async Task<ResponseContext<int>> Update(int id, [FromBody] BankDealUpdateDto dto)
        {
            if (!ModelState.IsValid)
                return new ResponseContext<int> { code = 400, state = ResponseType.error, message = "参数验证失败" };
            var rows = await _service.UpdateAsync(id, dto);
            return new ResponseContext<int> { code = 200, data = rows };
        }

        [HttpDelete("{id:int}")]
        public async Task<ResponseContext<int>> Delete(int id)
        {
            var rows = await _service.DeleteAsync(id);
            return new ResponseContext<int> { code = 200, data = rows };
        }
    }
}
