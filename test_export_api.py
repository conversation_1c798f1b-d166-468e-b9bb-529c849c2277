#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import requests
import json
from datetime import datetime

def test_export_api():
    """测试ExportDynamicUnifiedDailyReport API接口"""
    
    # API配置
    base_url = "http://localhost:5000"  # 根据实际端口调整
    endpoint = "/api/Reports/ExportDynamicUnifiedDailyReport"
    
    # 测试参数
    test_params = {
        "BeginDate": "20250817",
        "EndDate": "20250817", 
        "ShopId": 11,
        "Lang": "ZH"
    }
    
    print("=" * 60)
    print("ExportDynamicUnifiedDailyReport API 测试")
    print("=" * 60)
    print(f"请求URL: {base_url}{endpoint}")
    print(f"请求参数: {test_params}")
    print()
    
    try:
        # 发送请求
        print("发送请求...")
        response = requests.get(f"{base_url}{endpoint}", params=test_params, timeout=30)
        
        print(f"响应状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        print()
        
        if response.status_code == 200:
            # 成功响应
            content_type = response.headers.get('content-type', '')
            
            if 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' in content_type:
                # Excel文件
                filename = f"营业报表_{test_params['BeginDate']}_to_{test_params['EndDate']}.xlsx"
                with open(filename, 'wb') as f:
                    f.write(response.content)
                
                print(f"✅ 成功导出Excel文件: {filename}")
                print(f"文件大小: {len(response.content)} 字节")
                
                # 尝试验证Excel文件
                try:
                    import openpyxl
                    wb = openpyxl.load_workbook(filename)
                    ws = wb.active
                    print(f"Excel验证成功:")
                    print(f"  - 工作表名称: {ws.title}")
                    print(f"  - 最大行数: {ws.max_row}")
                    print(f"  - 最大列数: {ws.max_column}")
                    
                    # 显示前几行内容
                    print(f"  - 前3行内容:")
                    for row in range(1, min(4, ws.max_row + 1)):
                        row_data = []
                        for col in range(1, min(6, ws.max_column + 1)):
                            cell_value = ws.cell(row=row, column=col).value
                            if cell_value is not None:
                                row_data.append(str(cell_value))
                        if row_data:
                            print(f"    第{row}行: {' | '.join(row_data)}")
                    
                except ImportError:
                    print("⚠️  未安装openpyxl，无法验证Excel文件内容")
                except Exception as e:
                    print(f"❌ Excel文件验证失败: {e}")
                    
            else:
                print(f"❌ 意外的响应类型: {content_type}")
                print(f"响应内容: {response.text[:500]}")
                
        else:
            # 错误响应
            print(f"❌ 请求失败，状态码: {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误信息: {json.dumps(error_data, indent=2, ensure_ascii=False)}")
            except:
                print(f"错误内容: {response.text}")
                
    except requests.exceptions.ConnectionError:
        print("❌ 连接失败，请确保API服务正在运行")
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    print("\n" + "=" * 60)

def test_different_params():
    """测试不同参数组合"""
    
    test_cases = [
        {
            "name": "中文语言测试",
            "params": {"BeginDate": "20250817", "EndDate": "20250817", "ShopId": 11, "Lang": "ZH"}
        },
        {
            "name": "英文语言测试", 
            "params": {"BeginDate": "20250817", "EndDate": "20250817", "ShopId": 11, "Lang": "EN"}
        },
        {
            "name": "日期范围测试",
            "params": {"BeginDate": "20250815", "EndDate": "20250817", "ShopId": 11, "Lang": "ZH"}
        },
        {
            "name": "无效日期格式测试",
            "params": {"BeginDate": "2025-08-17", "EndDate": "2025-08-17", "ShopId": 11, "Lang": "ZH"}
        },
        {
            "name": "缺失参数测试",
            "params": {"ShopId": 11, "Lang": "ZH"}
        }
    ]
    
    base_url = "http://localhost:5000"
    endpoint = "/api/Reports/ExportDynamicUnifiedDailyReport"
    
    print("=" * 60)
    print("参数测试")
    print("=" * 60)
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n{i}. {test_case['name']}")
        print(f"参数: {test_case['params']}")
        
        try:
            response = requests.get(f"{base_url}{endpoint}", params=test_case['params'], timeout=10)
            print(f"状态码: {response.status_code}")
            
            if response.status_code == 200:
                content_type = response.headers.get('content-type', '')
                if 'excel' in content_type or 'spreadsheet' in content_type:
                    print(f"✅ 成功返回Excel文件 ({len(response.content)} 字节)")
                else:
                    print(f"⚠️  返回类型: {content_type}")
            else:
                try:
                    error_data = response.json()
                    print(f"❌ 错误: {error_data.get('message', '未知错误')}")
                except:
                    print(f"❌ 错误: {response.text[:100]}")
                    
        except Exception as e:
            print(f"❌ 请求失败: {e}")

if __name__ == "__main__":
    print("开始测试ExportDynamicUnifiedDailyReport API...")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 基本功能测试
    test_export_api()
    
    # 参数测试
    test_different_params()
    
    print("\n测试完成！")
    print("\n注意事项:")
    print("1. 确保API服务正在运行")
    print("2. 根据实际端口调整base_url")
    print("3. 确保数据库连接正常")
    print("4. 检查存储过程是否存在数据")
