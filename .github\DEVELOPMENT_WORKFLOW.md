# HdProject 完整开发工作流程文档

## 1. 项目概述与技术栈

### 项目架构
- **架构模式**: 基于 DDD (Domain-Driven Design) 的多层架构
- **主要技术栈**: 
  - 后端: ASP.NET Core + C# + SqlSugar ORM
  - 认证: JWT (JSON Web Tokens)
  - 日志: NLog
  - 对象映射: AutoMapper

### 项目层级结构
```
HdProject/
├── HdProject.Web.Api.Core/     # Web API 层 (控制器)
├── HdProject.Web.Core/         # Web 基础层 (基类控制器)
├── HdProject.Application/      # 应用服务层
├── HdProject.Domain/          # 领域层 (实体、DTO)
├── HdProject.Infrastructure/  # 基础设施层 (数据访问)
├── HdProject.Common/         # 公共组件层
├── HdProject.Core/           # 核心层
└── HdProject.Tests/          # 测试层
```

## 2. 接口命名规范分析与总结

### 当前项目接口命名模式分析

#### 2.1 控制器命名规范
- **命名格式**: `{功能名称}Controller`
- **示例**: 
  - `AuthController` (认证控制器)
  - `UserController` (用户控制器)
  - `BankDealController` (银行交易控制器)
  - `ReportsController` (报表控制器)

#### 2.2 路由命名规范 (已标准化)
基于之前的标准化工作，项目已统一使用 **PascalCase** 命名规范：

**标准CRUD操作路由：**
- `GET /api/{Controller}/GetAll` - 获取所有数据 (分页)
- `GET /api/{Controller}/GetById` - 根据ID获取单条数据
- `POST /api/{Controller}/Add` - 新增数据
- `PUT /api/{Controller}/Update` - 更新数据
- `DELETE /api/{Controller}/Delete` - 删除数据

**特殊功能路由：**
- `POST /api/Auth/Login` - 登录
- `POST /api/Auth/RefreshToken` - 刷新令牌
- `POST /api/Auth/ChangePassword` - 修改密码
- `GET /api/Auth/GetUserId` - 获取用户ID
- `GET /api/Reports/GetBankSummary` - 获取银行汇总
- `GET /api/Reports/ExportBankSummary` - 导出银行汇总

#### 2.3 方法命名规范
**控制器方法命名模式：**
- `{Entity}AddValue` - 新增操作
- `{Entity}UpdateValue` - 更新操作  
- `{Entity}DeletedValue` - 删除操作
- `GetById{Entity}` - 根据ID查询
- `GetAll{Entity}` - 查询所有

**示例：**
```csharp
[HttpPost("Add")]
public async Task<IActionResult> RoomAddValue([FromBody] RoomAddRequestDto request)

[HttpPut("Update")]  
public async Task<IActionResult> RoomUpdateValue([FromBody] RoomUpdateRequestDto request)

[HttpDelete("Delete")]
public async Task<IActionResult> RoomDeletedValue([FromBody] RoomDeleteRequestDto request)

[HttpGet("GetById")]
public async Task<IActionResult> GetByIdRoom([FromQuery] RoomGetByIdAsyncRequestDto request)
```

#### 2.4 参数命名规范
- **查询参数**: 使用 `[FromQuery]` 特性，采用 PascalCase
- **请求体参数**: 使用 `[FromBody]` 特性，通常命名为 `request`
- **路径参数**: 使用 `[FromRoute]` 或方法参数，采用 camelCase

## 3. 项目代码规范分析

### 3.1 文件组织结构规范

#### 控制器层 (HdProject.Web.Api.Core)
```
Controllers/
├── AuthController.cs                    # 认证控制器 (根目录)
├── UserController.cs                    # 用户控制器 (根目录)
├── {业务模块}/                          # 按业务模块分组
│   ├── {子模块}/
│   │   └── {功能}Controller.cs
│   └── ...
├── GrouponBase/Banking/                 # 团购基础-银行模块
│   ├── BankController.cs
│   └── BankDealController.cs
├── MainFood/Room/                       # 主餐-房间模块
│   ├── RoomManageController.cs
│   └── RoomTypeManageController.cs
├── SaasPos/                            # SaaS POS 模块
│   ├── MaterialManagement/
│   ├── Commission/
│   └── SongManagement/
└── Reports/                            # 报表模块
    └── ReportsController.cs
```

#### 服务层 (HdProject.Application)
```
Services/
├── Interfaces/                         # 接口定义
│   ├── I{Service}Service.cs           # 通用接口
│   └── {业务模块}/                     # 按业务模块分组接口
│       └── I{功能}Service.cs
├── {业务模块}/                         # 按业务模块实现
│   └── {功能}Service.cs
├── UserService.cs                     # 核心服务 (根目录)
├── JwtService.cs                      # JWT服务 (根目录)
└── Reports/                          # 报表服务
    └── ReportService.cs
```

#### DTO层 (HdProject.Common/DTOs)
```
DTOs/
├── {通用}Dto.cs                        # 通用DTO (根目录)
├── UserLoginDto.cs
├── TokenResponseDto.cs
└── {业务模块}/                         # 按业务模块分组
    └── {功能}Dtos.cs                   # 功能相关的所有DTO
```

### 3.2 命名空间规范
```csharp
// 控制器命名空间
namespace HdProject.Web.Api.Core.Controllers.{业务模块}.{子模块}

// 服务命名空间  
namespace HdProject.Application.Services.{业务模块}.{子模块}

// 接口命名空间
namespace HdProject.Application.Services.Interfaces.{业务模块}.{子模块}

// DTO命名空间
namespace HdProject.Common.DTOs.{业务模块}

// 实体命名空间
namespace HdProject.Domain.Entities.{业务模块}
```

### 3.3 类命名规范
- **控制器类**: `{功能名称}Controller`
- **服务类**: `{功能名称}Service`
- **接口类**: `I{功能名称}Service`
- **DTO类**: `{功能名称}{操作类型}Dto`
  - 查询: `{Entity}QueryDto`
  - 创建: `{Entity}CreateDto`
  - 更新: `{Entity}UpdateDto`
  - 请求: `{Entity}{Action}RequestDto`

### 3.4 控制器基类使用规范
```csharp
// 需要认证的控制器
public class SomeController : ControllerApiBase

// 匿名访问的控制器
public class PublicController : PublicControllerBase  

// 仅管理员访问的控制器
public class AdminController : AdminControllerBase
```

## 4. Git提交规范制定

### 4.1 Commit 消息格式
采用 **Conventional Commits** 规范：

```
<type>(<scope>): <subject>

<body>

<footer>
```

#### Type 类型
- `feat`: 新功能 (feature)
- `fix`: 修复bug
- `docs`: 文档变更
- `style`: 代码格式化 (不影响功能)
- `refactor`: 重构 (既不修复bug也不添加功能)
- `perf`: 性能优化
- `test`: 添加测试
- `chore`: 构建过程或辅助工具的变动

#### Scope 范围
按照项目模块分类：
- `auth`: 认证模块
- `user`: 用户模块
- `banking`: 银行模块
- `reports`: 报表模块
- `api`: API相关
- `config`: 配置相关
- `deps`: 依赖管理

#### 示例
```bash
# 新功能
feat(banking): 添加银行交易查询接口
- 新增BankDealController和相关服务
- 实现分页查询和多条件筛选
- 添加相应的DTO和验证规则

# 修复bug
fix(api): 修复银行交易查询中多个bankSKs参数的SQL错误
- 优化BankDealService中的LINQ查询条件
- 解决SqlSugar在处理Contains操作时的SQL生成问题

# 重构
refactor(api): 标准化API路由命名规范
- 将所有接口路由统一为PascalCase格式
- 更新AuthController, UserController, ReportsController路由
- 确保接口命名的一致性和可维护性

# 文档
docs: 更新开发工作流程文档
- 添加接口命名规范和Git提交规范
- 完善项目结构说明和代码模板

# 配置
chore(deps): 升级SqlSugar到最新版本
```

### 4.2 分支管理规范
- `main/master`: 主分支，生产环境代码
- `develop`: 开发分支，集成最新功能
- `feature/{功能名称}`: 功能分支
- `hotfix/{问题描述}`: 热修复分支
- `release/{版本号}`: 发布分支

### 4.3 提交频率建议
- **最小单元提交**: 每完成一个独立的功能点就提交
- **功能完整提交**: 确保每次提交都是功能完整的
- **日常提交**: 建议每天至少提交一次，避免代码丢失

## 5. 新增C#文件的标准化流程

### 5.1 控制器文件模板

#### 标准控制器模板
```csharp
using Microsoft.AspNetCore.Mvc;
using HdProject.Web.Core;
using HdProject.Application.Services.Interfaces.{业务模块};
using HdProject.Common.DTOs.{业务模块};

namespace HdProject.Web.Api.Core.Controllers.{业务模块}.{子模块}
{
    /// <summary>
    /// {功能描述}接口控制器
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class {Entity}Controller : PublicControllerBase
    {
        private readonly I{Entity}Service _service;

        public {Entity}Controller(I{Entity}Service service)
        {
            _service = service;
        }

        /// <summary>
        /// 查询所有{实体名称}的接口
        /// </summary>
        /// <param name="query">查询参数</param>
        /// <returns></returns>
        [HttpGet("GetAll")]
        public async Task<IActionResult> GetAll([FromQuery] {Entity}QueryDto query)
        {
            var list = await _service.GetListAsync(query);
            var total = await _service.CountAsync(/* 查询条件 */);
            return ApiPaged(list, query);
        }

        /// <summary>
        /// 根据ID查询{实体名称}的接口
        /// </summary>
        /// <param name="request">查询请求</param>
        /// <returns></returns>
        [HttpGet("GetById")]
        public async Task<IActionResult> GetById([FromQuery] {Entity}GetByIdRequestDto request)
        {
            var result = await _service.GetByIdAsync(request.Id);
            return ApiData(result);
        }

        /// <summary>
        /// 新增{实体名称}的接口
        /// </summary>
        /// <param name="request">新增请求</param>
        /// <returns></returns>
        [HttpPost("Add")]
        public async Task<IActionResult> {Entity}AddValue([FromBody] {Entity}CreateDto request)
        {
            if (!ModelState.IsValid)
                return ApiError("参数验证失败", 400);
                
            var result = await _service.CreateAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 更新{实体名称}的接口
        /// </summary>
        /// <param name="request">更新请求</param>
        /// <returns></returns>
        [HttpPut("Update")]
        public async Task<IActionResult> {Entity}UpdateValue([FromBody] {Entity}UpdateDto request)
        {
            if (!ModelState.IsValid)
                return ApiError("参数验证失败", 400);
                
            var result = await _service.UpdateAsync(request);
            return ApiData(result);
        }

        /// <summary>
        /// 删除{实体名称}的接口
        /// </summary>
        /// <param name="request">删除请求</param>
        /// <returns></returns>
        [HttpDelete("Delete")]
        public async Task<IActionResult> {Entity}DeletedValue([FromBody] {Entity}DeleteRequestDto request)
        {
            var result = await _service.DeleteAsync(request.Id);
            return ApiData(result);
        }
    }
}
```

### 5.2 服务层模板

#### 服务接口模板
```csharp
using HdProject.Common.DTOs.{业务模块};
using HdProject.Domain.Entities.{业务模块};

namespace HdProject.Application.Services.Interfaces.{业务模块}
{
    public interface I{Entity}Service
    {
        Task<List<{Entity}>> GetListAsync({Entity}QueryDto query);
        Task<{Entity}?> GetByIdAsync(int id);
        Task<int> CreateAsync({Entity}CreateDto dto);
        Task<int> UpdateAsync({Entity}UpdateDto dto);
        Task<int> DeleteAsync(int id);
        Task<int> CountAsync(/* 查询条件参数 */);
    }
}
```

#### 服务实现模板
```csharp
using HdProject.Application.Services.Interfaces.{业务模块};
using HdProject.Common.DTOs.{业务模块};
using HdProject.Domain.Entities.{业务模块};
using HdProject.Domain.Interfaces;
using HdProject.Domain.Result.Page;

namespace HdProject.Application.Services.{业务模块}
{
    public class {Entity}Service : I{Entity}Service
    {
        private readonly IRepositoryOperateData<{Entity}> _repo;

        public {Entity}Service(IRepositoryOperateData<{Entity}> repo)
        {
            _repo = repo;
        }

        public async Task<List<{Entity}>> GetListAsync({Entity}QueryDto query)
        {
            var page = query as Pagination;
            return await _repo.GetPageListAsync(page, entity =>
                // 添加查询条件
                true
            );
        }

        public async Task<{Entity}?> GetByIdAsync(int id)
        {
            return await _repo.GetByIdAsync(id);
        }

        public async Task<int> CreateAsync({Entity}CreateDto dto)
        {
            var entity = new {Entity}
            {
                // 映射属性
            };
            return await _repo.InsertAsync(entity);
        }

        public async Task<int> UpdateAsync({Entity}UpdateDto dto)
        {
            var entity = await _repo.GetByIdAsync(dto.Id);
            if (entity == null) return 0;
            
            // 更新属性
            
            return await _repo.UpdateAsync(entity);
        }

        public async Task<int> DeleteAsync(int id)
        {
            return await _repo.DeleteAsync(id);
        }

        public async Task<int> CountAsync(/* 查询条件参数 */)
        {
            return await _repo.CountAsync(entity =>
                // 添加查询条件
                true
            );
        }
    }
}
```

### 5.3 DTO模板

#### DTO文件模板 (一个功能的所有DTO)
```csharp
using System.ComponentModel.DataAnnotations;
using HdProject.Domain.Result.Page;

namespace HdProject.Common.DTOs.{业务模块}
{
    /// <summary>
    /// {实体名称}创建DTO
    /// </summary>
    public class {Entity}CreateDto
    {
        [Required, StringLength(200)]
        public string Name { get; set; } = string.Empty;
        
        // 其他必要属性
    }

    /// <summary>
    /// {实体名称}更新DTO
    /// </summary>
    public class {Entity}UpdateDto
    {
        [Required]
        public int Id { get; set; }
        
        [Required, StringLength(200)]
        public string Name { get; set; } = string.Empty;
        
        // 其他可更新属性
    }

    /// <summary>
    /// {实体名称}查询DTO
    /// </summary>
    public class {Entity}QueryDto : Pagination
    {
        public string? Keyword { get; set; }
        
        // 其他查询条件
    }

    /// <summary>
    /// {实体名称}根据ID查询请求DTO
    /// </summary>
    public class {Entity}GetByIdRequestDto
    {
        [Required]
        public int Id { get; set; }
    }

    /// <summary>
    /// {实体名称}删除请求DTO
    /// </summary>
    public class {Entity}DeleteRequestDto
    {
        [Required]
        public int Id { get; set; }
    }
}
```

### 5.4 文件创建流程

#### 步骤一：创建服务接口
1. 在 `HdProject.Application/Services/Interfaces/{业务模块}/` 创建 `I{Entity}Service.cs`
2. 使用服务接口模板
3. 定义必要的方法签名

#### 步骤二：创建DTO
1. 在 `HdProject.Common/DTOs/{业务模块}/` 创建 `{Entity}Dtos.cs`
2. 使用DTO模板
3. 根据实际需求调整属性和验证规则

#### 步骤三：创建服务实现
1. 在 `HdProject.Application/Services/{业务模块}/` 创建 `{Entity}Service.cs`
2. 使用服务实现模板
3. 实现业务逻辑

#### 步骤四：创建控制器
1. 在 `HdProject.Web.Api.Core/Controllers/{业务模块}/{子模块}/` 创建 `{Entity}Controller.cs`
2. 使用控制器模板
3. 配置正确的路由和权限

#### 步骤五：注册依赖注入
在 `Program.cs` 中注册服务：
```csharp
builder.Services.AddScoped<I{Entity}Service, {Entity}Service>();
```

#### 步骤六：Git提交
使用标准的commit格式提交代码：
```bash
feat({module}): 添加{Entity}管理功能
- 新增{Entity}Controller控制器
- 实现{Entity}Service服务层
- 添加相关DTO和验证规则
- 完成CRUD基础操作
```

## 6. 开发最佳实践

### 6.1 代码质量要求
- **代码注释**: 所有公共方法必须有XML文档注释
- **异常处理**: 使用try-catch处理可能的异常
- **参数验证**: 使用ModelState验证和数据注解
- **日志记录**: 关键操作需要记录日志

### 6.2 性能优化建议
- **异步操作**: 所有数据库操作使用async/await
- **分页查询**: 列表查询必须支持分页
- **缓存策略**: 对于频繁查询的数据考虑使用缓存

### 6.3 安全性要求
- **认证授权**: 根据业务需求选择合适的控制器基类
- **输入验证**: 所有用户输入必须验证
- **SQL注入防护**: 使用参数化查询

---

**文档版本**: 1.0  
**创建时间**: 2025-08-14  
**维护者**: HdProject开发团队  
**更新说明**: 基于当前项目结构分析制定的标准化开发流程
