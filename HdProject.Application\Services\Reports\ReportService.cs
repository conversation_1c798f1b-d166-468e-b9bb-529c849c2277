using System.Globalization;
using HdProject.Application.Services.Interfaces.Reports;
using HdProject.Domain.Context.Reports;
using SqlSugar;
using ClosedXML.Excel;
using System.Text.RegularExpressions;

namespace HdProject.Application.Services.Reports
{
    // 辅助类定义
    public class ProcessedReportData
    {
        public List<Dictionary<string, object>> BaseData { get; set; } = new();
        public List<TimeSlotInfo> TimeSlots { get; set; } = new();
    }

    public class TimeSlotInfo
    {
        public string TimeSlot { get; set; } = string.Empty;
        public Dictionary<string, List<object>> Fields { get; set; } = new();
    }

    public class ReportService : IReportService
    {
        private readonly IBankSummaryDal _dal;
        private readonly ISqlSugarClient _sqlSugarClient;

        public ReportService(IBankSummaryDal dal, ISqlSugarClient sqlSugarClient)
        {
            _dal = dal;
            _sqlSugarClient = sqlSugarClient;
        }

        public async Task<BankSummaryResponseDto> GenerateBankSummaryReportAsync(BankSummaryRequestDto request)
        {
            // 1) 解析日期
            if (!DateTime.TryParseExact(request.StartDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var startDate))
                throw new ArgumentException("StartDate 格式错误，应为 yyyy-MM-dd");
            if (!DateTime.TryParseExact(request.EndDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var endDate))
                throw new ArgumentException("EndDate 格式错误，应为 yyyy-MM-dd");

            // 2) 执行 SQL（扁平结果）
            var flat = await _dal.GetFlatAsync(startDate, endDate, request.BankSKs);

            // 3) 塑形为响应结构
            return Pivot(flat);
        }

        public async Task<(byte[] Bytes, string FileName, string ContentType)> ExportBankSummaryXlsxAsync(BankSummaryRequestDto request, string? titleDateRange = null)
        {
            var data = await GenerateBankSummaryReportAsync(request);

            using var wb = new XLWorkbook();
            var ws = wb.Worksheets.Add("银行汇总表");

            int col = 1;
            int row = 1;

            // 标题（格式：yyyy年M月d日），不展示“银行：”
            if (!DateTime.TryParseExact(request.StartDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var start))
                throw new ArgumentException("StartDate 格式错误，应为 yyyy-MM-dd");
            if (!DateTime.TryParseExact(request.EndDate, "yyyy-MM-dd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var end))
                throw new ArgumentException("EndDate 格式错误，应为 yyyy-MM-dd");
            var startZh = start.ToString("yyyy年M月d日");
            var endZh = end.ToString("yyyy年M月d日");

            ws.Cell(row, col).Value = $"日期：{startZh} 至 {endZh}";
            ws.Range(row, col, row, 1 + data.ColumnHeaders.Count * 5 + 4).Merge().Style
                .Font.SetBold().Font.SetFontSize(12);
            row += 2;

            // 叠表头 第一行
            ws.Cell(row, col).Value = "银行券名称/门店";
            ws.Range(row, col, row + 1, col).Merge().Style.Alignment.SetVertical(XLAlignmentVerticalValues.Center)
              .Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            col++;
            foreach (var shop in data.ColumnHeaders)
            {
                ws.Cell(row, col).Value = shop.ShopName;
                // 每店 5 列：核销量、核销额、补贴额、服务费、合计(核销额+补贴额+服务费)
                ws.Range(row, col, row, col + 4).Merge().Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);
                ws.Cell(row + 1, col).Value = "核销量";
                ws.Cell(row + 1, col + 1).Value = "核销额";
                ws.Cell(row + 1, col + 2).Value = "补贴额";
                ws.Cell(row + 1, col + 3).Value = "服务费";
                ws.Cell(row + 1, col + 4).Value = "合计";
                col += 5;
            }

            // 尾部汇总四列
            ws.Cell(row, col).Value = "合计核销额";
            ws.Cell(row, col + 1).Value = "补贴金额";
            ws.Cell(row, col + 2).Value = "平台服务费";
            ws.Cell(row, col + 3).Value = "合计实收金额";
            ws.Range(row, col, row, col + 3).Style.Alignment.SetHorizontal(XLAlignmentHorizontalValues.Center);

            row += 2;

            // 数据行
            foreach (var r in data.Rows)
            {
                col = 1;
                ws.Cell(row, col++).Value = r.DealName;
                foreach (var shop in data.ColumnHeaders)
                {
                    r.ShopData.TryGetValue(shop.ShopID, out var cell);
                    var cnt = cell?.Count ?? 0;
                    var amt = cell?.Amount ?? 0m;
                    var sub = cell?.Subsidy ?? 0m;
                    var fee = cell?.PlatformFee ?? 0m;
                    ws.Cell(row, col++).Value = cnt;
                    ws.Cell(row, col++).Value = amt;
                    ws.Cell(row, col++).Value = sub;
                    ws.Cell(row, col++).Value = fee;
                    ws.Cell(row, col++).Value = amt + sub - fee;
                }
                // 行尾合计：使用 RowTotal 中的 Amount 和 Subsidy
                var rowAmtSum = r.RowTotal.Amount;
                var rowSubSum = r.RowTotal.Subsidy;
                var rowFeeSum = r.RowTotal.PlatformFee;
                var rowNetSum = r.RowTotal.NetAmount;
                ws.Cell(row, col++).Value = rowAmtSum;
                ws.Cell(row, col++).Value = rowSubSum;
                ws.Cell(row, col++).Value = rowFeeSum;
                ws.Cell(row, col++).Value = rowNetSum;
                row++;
            }

            // 合计行
            col = 1;
            ws.Cell(row, col).Value = "合计";
            ws.Range(row, col, row, col).Style.Font.SetBold();
            col++;
            foreach (var shop in data.ColumnHeaders)
            {
                var cnt = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.Count : 0);
                var amt = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.Amount : 0m);
                var sub = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.Subsidy : 0m);
                var fee = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.PlatformFee : 0m);
                var net = data.Rows.Sum(rr => rr.ShopData.TryGetValue(shop.ShopID, out var c) ? c.NetAmount : 0m);
                ws.Cell(row, col++).Value = cnt;
                ws.Cell(row, col++).Value = amt;
                ws.Cell(row, col++).Value = sub;
                ws.Cell(row, col++).Value = fee;
                ws.Cell(row, col++).Value = net;
            }
            ws.Cell(row, col++).Value = data.GrandTotal.TotalAmount;
            ws.Cell(row, col++).Value = data.GrandTotal.TotalSubsidy;
            ws.Cell(row, col++).Value = data.GrandTotal.TotalPlatformFee;
            ws.Cell(row, col++).Value = data.GrandTotal.TotalNetAmount;

            // 样式
            var lastCol = 1 + data.ColumnHeaders.Count * 5 + 4;
            var lastRow = row;
            ws.Range(3, 1, lastRow, lastCol).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            ws.Range(3, 1, lastRow, lastCol).Style.Border.InsideBorder = XLBorderStyleValues.Thin;
            ws.Columns().AdjustToContents();

            using var ms = new MemoryStream();
            wb.SaveAs(ms);
            var bytes = ms.ToArray();
            var fileName = $"银行汇总表_{request.StartDate}_to_{request.EndDate}.xlsx";
            return (bytes, fileName, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        private static BankSummaryResponseDto Pivot(List<FlatReportDataDto> flat)
        {
            var resp = new BankSummaryResponseDto();

            // 列头（去重的门店）
            var shops = flat
                .GroupBy(x => new { x.ShopID, x.ShopName })
                .Select(g => new ShopHeaderDto { ShopID = g.Key.ShopID, ShopName = g.Key.ShopName })
                .OrderBy(s => s.ShopName)
                .ToList();
            resp.ColumnHeaders = shops;

            // 行（券）
            var byDeal = flat.GroupBy(x => new { x.DealSK, x.DealName })
                .OrderBy(g => g.Key.DealName);

            foreach (var dealGroup in byDeal)
            {
                var row = new BankSummaryRowDto
                {
                    DealSK = dealGroup.Key.DealSK,
                    DealName = dealGroup.Key.DealName,
                    ShopData = new Dictionary<int, ShopCellDto>()
                };

                int rowCount = 0;
                decimal rowAmount = 0m, rowSubsidy = 0m, rowPlatformFee = 0m, rowNetAmount = 0m;

                // 为所有列头填充
                foreach (var shop in shops)
                {
                    var cellAgg = dealGroup.Where(x => x.ShopID == shop.ShopID);
                    var cell = new ShopCellDto
                    {
                        Count = cellAgg.Sum(x => x.TotalCount),
                        Amount = cellAgg.Sum(x => x.TotalAmount),
                        Subsidy = cellAgg.Sum(x => x.TotalSubsidy),
                        PlatformFee = cellAgg.Sum(x => x.TotalPlatformFee),
                        NetAmount = cellAgg.Sum(x => x.TotalNetAmount)
                    };
                    row.ShopData[shop.ShopID] = cell;

                    rowCount += cell.Count;
                    rowAmount += cell.Amount;
                    rowSubsidy += cell.Subsidy;
                    rowPlatformFee += cell.PlatformFee;
                    rowNetAmount += cell.NetAmount;
                }

                row.RowTotal = new ShopTotalsDto { Count = rowCount, Amount = rowAmount, Subsidy = rowSubsidy, PlatformFee = rowPlatformFee, NetAmount = rowNetAmount };
                resp.Rows.Add(row);
            }

            // 总计
            resp.GrandTotal = new BankSummaryGrandTotalDto
            {
                TotalAmount = flat.Sum(x => x.TotalAmount),
                TotalSubsidy = flat.Sum(x => x.TotalSubsidy),
                TotalPlatformFee = flat.Sum(x => x.TotalPlatformFee),
                TotalNetAmount = flat.Sum(x => x.TotalNetAmount)
            };

            return resp;
        }

        public async Task<(byte[] Bytes, string FileName, string ContentType)> ExportDynamicUnifiedDailyReportAsync(DynamicUnifiedDailyReportRequestDto request)
        {
            // 1) 参数验证和日期转换
            if (!DateTime.TryParseExact(request.BeginDate, "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var beginDate))
                throw new ArgumentException("BeginDate 格式错误，应为 yyyyMMdd");
            if (!DateTime.TryParseExact(request.EndDate, "yyyyMMdd", CultureInfo.InvariantCulture, DateTimeStyles.None, out var endDate))
                throw new ArgumentException("EndDate 格式错误，应为 yyyyMMdd");

            // 2) 调用存储过程获取数据
            var operateDataDb = _sqlSugarClient.AsTenant().GetConnection("OperateData");
            var parameters = new List<SugarParameter>
            {
                new SugarParameter("@ShopId", request.ShopId),
                new SugarParameter("@BeginDate", request.BeginDate),
                new SugarParameter("@EndDate", request.EndDate)
            };

            var rawData = await operateDataDb.Ado.UseStoredProcedure()
                .SqlQueryAsync<Dictionary<string, object>>("dbo.usp_GenerateDynamicUnifiedDailyReport", parameters);

            if (rawData == null || !rawData.Any())
            {
                throw new InvalidOperationException("未查询到数据");
            }

            // 3) 生成Excel文件
            return GenerateExcelFile(rawData, request);
        }

        private (byte[] Bytes, string FileName, string ContentType) GenerateExcelFile(
            List<Dictionary<string, object>> rawData,
            DynamicUnifiedDailyReportRequestDto request)
        {
            using var wb = new XLWorkbook();
            var ws = wb.Worksheets.Add("营业报表");

            // 解析数据结构
            var processedData = ProcessDynamicData(rawData);

            // 生成表头和数据
            GenerateExcelHeaders(ws, processedData, request.Lang);
            GenerateExcelData(ws, processedData, request.Lang);

            // 设置样式
            ApplyExcelStyles(ws, processedData);

            // 保存文件
            using var ms = new MemoryStream();
            wb.SaveAs(ms);
            var bytes = ms.ToArray();

            var fileName = $"营业报表_{request.BeginDate}_to_{request.EndDate}.xlsx";
            return (bytes, fileName, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        }

        private ProcessedReportData ProcessDynamicData(List<Dictionary<string, object>> rawData)
        {
            var result = new ProcessedReportData
            {
                BaseData = new List<Dictionary<string, object>>(),
                TimeSlots = new List<TimeSlotInfo>()
            };

            var timeSlotFields = new Dictionary<string, Dictionary<string, List<object>>>();

            foreach (var row in rawData)
            {
                var baseRow = new Dictionary<string, object>();
                var timeSlotRow = new Dictionary<string, Dictionary<string, object>>();

                foreach (var kvp in row)
                {
                    // 匹配时间段字段格式: "HH:MM-HH:MM_字段名"
                    var timeMatch = Regex.Match(kvp.Key, @"^(\d{2}:\d{2}-\d{2}:\d{2})_(.+)$");

                    if (timeMatch.Success)
                    {
                        var timeSlot = timeMatch.Groups[1].Value;
                        var fieldName = timeMatch.Groups[2].Value;

                        if (!timeSlotFields.ContainsKey(timeSlot))
                            timeSlotFields[timeSlot] = new Dictionary<string, List<object>>();

                        if (!timeSlotFields[timeSlot].ContainsKey(fieldName))
                            timeSlotFields[timeSlot][fieldName] = new List<object>();

                        timeSlotFields[timeSlot][fieldName].Add(kvp.Value ?? 0);
                    }
                    else
                    {
                        baseRow[kvp.Key] = kvp.Value;
                    }
                }

                result.BaseData.Add(baseRow);
            }

            // 转换时间段数据
            foreach (var timeSlot in timeSlotFields.Keys.OrderBy(x => x))
            {
                result.TimeSlots.Add(new TimeSlotInfo
                {
                    TimeSlot = timeSlot,
                    Fields = timeSlotFields[timeSlot]
                });
            }

            return result;
        }

        private void GenerateExcelHeaders(IXLWorksheet ws, ProcessedReportData data, string lang)
        {
            var shopName = data.BaseData.FirstOrDefault()?.ContainsKey("门店") == true ?
                data.BaseData.First()["门店"]?.ToString() ?? "门店" : "门店";
            var dateRange = GetDateRangeFromData(data);

            // 第1行：标题行
            var title = lang == "ZH" ? $"【{shopName}】{dateRange}" : $"[{shopName}] {dateRange}";
            ws.Cell(1, 1).Value = title;
            ws.Cell(1, 1).Style.Font.Bold = true;
            ws.Cell(1, 1).Style.Font.FontSize = 14;

            // 第2行：大分组标题
            int col = 1;
            var groupHeaders = GetGroupHeaders(lang);
            var baseHeaderCount = GetBaseHeaders(lang).Count;
            var timeSlotFieldCount = GetTimeSlotFieldHeaders(lang).Count;

            // 基础信息分组
            ws.Cell(2, col).Value = groupHeaders["BasicInfo"];
            ws.Range(2, col, 2, col + 2).Merge(); // 日期、星期、门店
            col += 3;

            // 营收数据分组
            ws.Cell(2, col).Value = groupHeaders["Revenue"];
            ws.Range(2, col, 2, col + 2).Merge(); // 总收入、白天档、晚档
            col += 3;

            // 带客数据分组
            ws.Cell(2, col).Value = groupHeaders["BatchData"];
            ws.Range(2, col, 2, col + 6).Merge(); // 各种批次数据
            col += 7;

            // 用餐人数分组
            ws.Cell(2, col).Value = groupHeaders["GuestCount"];
            ws.Range(2, col, 2, col + 1).Merge(); // K+餐人数、直落人数
            col += 2;

            // K+自助餐分组（时间段数据）
            var timeSlotStartCol = col;
            ws.Cell(2, col).Value = groupHeaders["KPlusBuffet"];
            var totalTimeSlotCols = data.TimeSlots.Count * timeSlotFieldCount;
            if (totalTimeSlotCols > 0)
            {
                ws.Range(2, col, 2, col + totalTimeSlotCols - 1).Merge();
            }

            // 第3行：中分组（基础字段和时间段）
            col = 1;
            var baseHeaders = GetBaseHeaders(lang);
            foreach (var header in baseHeaders)
            {
                ws.Cell(3, col).Value = header.Value;
                ws.Range(3, col, 4, col).Merge(); // 合并到第4行
                col++;
            }

            // 时间段分组
            foreach (var timeSlot in data.TimeSlots)
            {
                var startCol = col;
                var fieldHeaders = GetTimeSlotFieldHeaders(lang);

                // 时间段名称（合并单元格）
                ws.Cell(3, startCol).Value = timeSlot.TimeSlot;
                ws.Range(3, startCol, 3, startCol + fieldHeaders.Count - 1).Merge();

                // 第4行：具体字段名称
                var fieldCol = startCol;
                foreach (var fieldHeader in fieldHeaders)
                {
                    ws.Cell(4, fieldCol).Value = fieldHeader.Value;
                    fieldCol++;
                }

                col = fieldCol;
            }

            // 设置表头样式
            ApplyHeaderStyles(ws, 4, col - 1);
        }

        private void GenerateExcelData(IXLWorksheet ws, ProcessedReportData data, string lang)
        {
            int startRow = 5; // 从第5行开始写数据（前4行是表头）

            for (int i = 0; i < data.BaseData.Count; i++)
            {
                int row = startRow + i;
                int col = 1;

                var baseRow = data.BaseData[i];
                var baseHeaders = GetBaseHeaders(lang);

                // 写入基础数据
                foreach (var header in baseHeaders)
                {
                    var value = baseRow.ContainsKey(header.Key) ? baseRow[header.Key] : "";

                    // 特殊处理日期格式
                    if (header.Key == "日期" && value != null)
                    {
                        value = FormatReportDate(value.ToString());
                    }

                    ws.Cell(row, col).Value = value?.ToString() ?? "";
                    col++;
                }

                // 写入时间段数据
                foreach (var timeSlot in data.TimeSlots)
                {
                    var fieldHeaders = GetTimeSlotFieldHeaders(lang);
                    foreach (var fieldHeader in fieldHeaders)
                    {
                        var value = 0;
                        if (timeSlot.Fields.ContainsKey(fieldHeader.Key) &&
                            timeSlot.Fields[fieldHeader.Key].Count > i)
                        {
                            value = Convert.ToInt32(timeSlot.Fields[fieldHeader.Key][i]);
                        }
                        ws.Cell(row, col).Value = value;
                        col++;
                    }
                }
            }
        }

        private void ApplyExcelStyles(IXLWorksheet ws, ProcessedReportData data)
        {
            // 计算总列数
            var baseHeaderCount = GetBaseHeaders("EN").Count;
            var timeSlotFieldCount = GetTimeSlotFieldHeaders("EN").Count;
            var totalCols = baseHeaderCount + (data.TimeSlots.Count * timeSlotFieldCount);
            var totalRows = data.BaseData.Count + 4; // 包含4行表头

            // 设置边框
            ws.Range(1, 1, totalRows, totalCols).Style.Border.OutsideBorder = XLBorderStyleValues.Thin;
            ws.Range(1, 1, totalRows, totalCols).Style.Border.InsideBorder = XLBorderStyleValues.Thin;

            // 自动调整列宽
            ws.Columns().AdjustToContents();
        }

        private void ApplyHeaderStyles(IXLWorksheet ws, int headerRows, int totalCols)
        {
            // 设置表头样式
            for (int row = 1; row <= headerRows; row++)
            {
                for (int col = 1; col <= totalCols; col++)
                {
                    var cell = ws.Cell(row, col);
                    cell.Style.Font.Bold = true;
                    cell.Style.Alignment.Horizontal = XLAlignmentHorizontalValues.Center;
                    cell.Style.Alignment.Vertical = XLAlignmentVerticalValues.Center;
                    cell.Style.Fill.BackgroundColor = XLColor.LightGray;
                }
            }
        }

        private Dictionary<string, string> GetBaseHeaders(string lang)
        {
            if (lang == "ZH")
            {
                return new Dictionary<string, string>
                {
                    { "日期", "日期" },
                    { "星期", "星期" },
                    { "营收_总收入", "总营业收入" },
                    { "营收_白天档", "白天档" },
                    { "营收_晚上档", "晚档" },
                    { "全天总批数", "全天总营客批数" },
                    { "白天档_总批次", "白天档K+餐" },
                    { "白天档_直落", "直落" },
                    { "晚上档_总批次", "20点后进场" },
                    { "晚上档_直落", "直落" },
                    { "自助餐人数", "K+餐人数" },
                    { "直落人数", "直落人数" }
                };
            }
            else
            {
                return new Dictionary<string, string>
                {
                    { "日期", "Date" },
                    { "星期", "Weekday" },
                    { "营收_总收入", "Total Revenue" },
                    { "营收_白天档", "Day Time" },
                    { "营收_晚上档", "Night Time" },
                    { "全天总批数", "Total Batch Count" },
                    { "白天档_总批次", "Day K+ Meal" },
                    { "白天档_直落", "Direct Fall" },
                    { "晚上档_总批次", "After 8PM" },
                    { "晚上档_直落", "Direct Fall" },
                    { "自助餐人数", "K+ Guest Count" },
                    { "直落人数", "Direct Fall Guests" }
                };
            }
        }

        private Dictionary<string, string> GetTimeSlotFieldHeaders(string lang)
        {
            if (lang == "ZH")
            {
                return new Dictionary<string, string>
                {
                    { "K+", "K+" },
                    { "特权预约", "特权预约" },
                    { "美团", "美团" },
                    { "抖音", "抖音" },
                    { "房费", "房费" },
                    { "小计", "小计" },
                    { "上档直落", "上一档直落批数" }
                };
            }
            else
            {
                return new Dictionary<string, string>
                {
                    { "K+", "K+" },
                    { "特权预约", "Special Reservation" },
                    { "美团", "Meituan" },
                    { "抖音", "Douyin" },
                    { "房费", "Room Fee" },
                    { "小计", "Subtotal" },
                    { "上档直落", "Prev Slot Direct Fall" }
                };
            }
        }

        private Dictionary<string, string> GetGroupHeaders(string lang)
        {
            if (lang == "ZH")
            {
                return new Dictionary<string, string>
                {
                    { "BasicInfo", "基础信息" },
                    { "Revenue", "每日营收数据" },
                    { "BatchData", "每日带客数据" },
                    { "GuestCount", "用餐人数" },
                    { "KPlusBuffet", "K+自助餐" }
                };
            }
            else
            {
                return new Dictionary<string, string>
                {
                    { "BasicInfo", "Basic Info" },
                    { "Revenue", "Daily Revenue Data" },
                    { "BatchData", "Daily Batch Data" },
                    { "GuestCount", "Guest Count" },
                    { "KPlusBuffet", "K+ Buffet" }
                };
            }
        }

        private string GetDateRangeFromData(ProcessedReportData data)
        {
            if (data.BaseData.Any())
            {
                var firstDate = data.BaseData.First().ContainsKey("日期") ?
                    data.BaseData.First()["日期"]?.ToString() : "";
                var lastDate = data.BaseData.Last().ContainsKey("日期") ?
                    data.BaseData.Last()["日期"]?.ToString() : "";

                if (!string.IsNullOrEmpty(firstDate))
                {
                    var formattedFirst = FormatReportDate(firstDate);
                    var formattedLast = FormatReportDate(lastDate);

                    if (formattedFirst == formattedLast)
                    {
                        return DateTime.TryParse(formattedFirst, out var date) ?
                            date.ToString("yyyy年MM月") : "营业报表";
                    }
                    else
                    {
                        return "营业报表";
                    }
                }
            }
            return "营业报表";
        }

        private string FormatReportDate(string dateString)
        {
            if (string.IsNullOrEmpty(dateString))
                return "";

            // 处理 .NET 日期格式 /Date(timestamp)/
            if (dateString.Contains("/Date("))
            {
                var match = Regex.Match(dateString, @"/Date\((\d+)\)/");
                if (match.Success && long.TryParse(match.Groups[1].Value, out var timestamp))
                {
                    var dateTime = DateTimeOffset.FromUnixTimeMilliseconds(timestamp).DateTime;
                    return dateTime.ToString("yyyy-MM-dd");
                }
            }

            return dateString;
        }
    }
}
