# HdProject 技术文档

## 项目架构

本项目采用分层/洋葱架构：
- Domain（领域层）：实体、领域接口、分页模型、结果类型；不依赖外部实现。
- Application（应用层）：用例/服务实现，编排仓储与事务、DTO 校验与转换。
- Infrastructure（基础设施层）：SqlSugar 多租连接、通用与分库仓储实现、外部服务。
- Web.Api（接口层）：ASP.NET Core Web API，统一异常处理、认证授权、Swagger 文档。
- Common/Core（共用层）：DTO、配置模型、日志封装、AutoMapper Profile 等。

数据访问通过 IRepository<T> 抽象与不同连接上下文的派生仓储（如 IRepositoryGroupBase<T>）解耦，上层只依赖接口；连接由 SqlSugar 多租 Id（ConfigId）选择。

## 技术栈

- 后端：.NET 8/9（ASP.NET Core Minimal Hosting）、SqlSugar、NLog、Swagger（Swashbuckle）、JWT 认证、AutoMapper
- 数据库：SQL Server（多连接：Default、Saas、MainFood、Rms、GroupBase 等）
- 前端：Vue 3（Vite）+ Element Plus + Axios

说明：当前解决方案已配置多数据库连接（见 `SqlSugarDbContext`），并在 `Program.cs` 完成了仓储与服务的依赖注入及 Swagger/JWT 配置。

## 开发环境配置

1) 安装
- Visual Studio 2022 17.10+ 或 VS Code + C# Dev Kit
- .NET SDK 8.0 或 9.0（建议 9.0，如使用预览/正式版请与 csproj 对齐）
- Node.js 18+（用于前端）

2) 数据库
- 在 `appsettings.json` 配置连接字符串：`Default`、`GroupBase` 等；与 `SqlSugarDbContext` 的 `ConfigId` 一致。
- 确保存在表：`Dim_Bank`、`Dim_Bank_Deal`（见“业务表结构”）。

3) 运行后端
- 设置启动项目：`HdProject.Web.Api.Core`
- 调试运行后访问 Swagger：`http://localhost:<port>/`（已配置 Dev 下默认首页）

4) 运行前端（如创建于 `frontend/`）
- 安装依赖并运行开发服务器（见下文“前端项目”章节）。

## 项目目录结构（关键）

- HdProject.Domain
  - Entities/…（领域实体）
  - Interfaces/…（仓储接口）
  - Result/Page/Pagination.cs
- HdProject.Infrastructure
  - DbContext/SqlSugarDbContext.cs（多连接配置）
  - Repositorys/Imp/…（通用仓储实现与分库仓储）
- HdProject.Application
  - Services/…（业务服务）
  - Services/Interfaces/…（服务接口）
- HdProject.Web.Api.Core
  - Controllers/…（Web API 控制器）
  - Program.cs（DI、认证、Swagger、CORS、静态文件）
- HdProject.Common
  - DTOs/Response.cs（标准响应）
  - Config/JwtSettings.cs, SqlLogSettings.cs

## 业务表结构（用于本次 CRUD）

- Dim_Bank（银行枚举表）
  - BankSK (int, PK)
  - BankName (nvarchar, not null) — 若实际列不同，请以真实库为准

- Dim_Bank_Deal（银行商品券关联表）
  - DealSK (int, PK)
  - BankSK (int, FK -> Dim_Bank)
  - FdNo (nvarchar)
  - DealName (nvarchar)
  - DealAmount (decimal(18,2))
  - SubsidyAmount (decimal(18,2))
  - TotalAmount (decimal(18,2))
  - ServiceFee (decimal(18,2))
  - NetAmount (decimal(18,2))

示例数据见需求描述（INSERT INTO Dim_Bank_Deal ...）。

## 后端 API 设计（REST）

- 银行 Bank
  - GET /api/banks?page&rows&sidx&sord&keyword
  - GET /api/banks/{id}
  - POST /api/banks
  - PUT /api/banks/{id}
  - DELETE /api/banks/{id}

- 银行商品券 BankDeal
  - GET /api/bank-deals?page&rows&sidx&sord&bankSk&fdNo&dealName
  - GET /api/bank-deals/{id}
  - POST /api/bank-deals
  - PUT /api/bank-deals/{id}
  - DELETE /api/bank-deals/{id}

返回模型：统一使用 `ResponseContext<T>` 包裹；分页采用 `Pagination`。

验证与错误处理：
- DTO 使用 DataAnnotations 标注必填/范围；
- 控制器在 `ModelState` 校验不通过时返回 400；
- 未捕获异常由 `GlobalExceptionFilter` 统一处理；
- Swagger 已启用，支持 Bearer 认证调试。

## 前端设计（Vue 3 + Element Plus）

- 页面
  - 银行商品券列表：表格 + 搜索（bankSk、fdNo、dealName）+ 分页 + 排序
  - 详情/编辑：弹窗或独立路由表单，含校验

- API 封装
  - 使用 Axios 实例设置 baseURL 与拦截器；
  - 模块化封装 bank、bankDeal 服务。

- 交互流程
  - 进入列表 -> 加载分页数据 -> 搜索/排序/翻页 -> 新增/编辑 -> 表单校验通过 -> 调用 API -> 成功提示并刷新。

## 安全与最佳实践

- 后端遵循 REST 风格、状态码语义清晰；
- DTO 输入校验、仓储层字段白名单排序（已在通用仓储实现中处理）；
- CORS 已全开（可按环境收敛域名）；
- 日志与 SQL 记录：NLog + SqlSugar AOP。

## 运行指引（简要）

后端：设置连接字符串并运行 `HdProject.Web.Api.Core`；
前端：在 `frontend/` 目录执行 `npm i` 与 `npm run dev`，在 .env 配置 API 基地址与 Token（如需）。
