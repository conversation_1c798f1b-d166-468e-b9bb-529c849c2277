{"profiles": {"http": {"commandName": "Project", "launchBrowser": true, "launchUrl": "", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "http://0.0.0.0:5005;http://0.0.0.0:5010"}, "https": {"commandName": "Project", "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}, "dotnetRunMessages": true, "applicationUrl": "https://0.0.0.0:7226;http://0.0.0.0:5005;http://0.0.0.0:5010"}, "IIS Express": {"commandName": "IISExpress", "launchBrowser": true, "environmentVariables": {"ASPNETCORE_ENVIRONMENT": "Development"}}}, "$schema": "https://json.schemastore.org/launchsettings.json", "iisSettings": {"windowsAuthentication": false, "anonymousAuthentication": true, "iisExpress": {"applicationUrl": "http://localhost:8691/;http://localhost:5010", "sslPort": 44359}}}