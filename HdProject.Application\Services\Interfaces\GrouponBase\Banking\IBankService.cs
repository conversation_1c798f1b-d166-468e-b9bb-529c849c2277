using HdProject.Common.DTOs.Banking;
using HdProject.Domain.Result.Page;
using HdProject.Domain.Entities.GroupBase;

namespace HdProject.Application.Services.Interfaces.GrouponBase.Banking
{
    public interface IBankService
    {
        Task<List<Dim_Bank>> GetListAsync(BankQueryDto query);
        Task<Dim_Bank?> GetByIdAsync(int id);
        Task<int> CreateAsync(BankCreateDto dto);
        Task<int> UpdateAsync(int id, BankUpdateDto dto);
        Task<int> DeleteAsync(int id);
        Task<int> CountAsync(string? keyword);
    }

    public interface IBankDealService
    {
        Task<List<Dim_Bank_Deal>> GetListAsync(BankDealQueryDto query);
        Task<Dim_Bank_Deal?> GetByIdAsync(int id);
        Task<int> CreateAsync(BankDealCreateDto dto);
        Task<int> UpdateAsync(int id, BankDealUpdateDto dto);
        Task<int> DeleteAsync(int id);
        Task<int> CountAsync(int[]? bankSks, string? fdNo, string? dealName);
    }
}
