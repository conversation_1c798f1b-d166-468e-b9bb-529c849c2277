using System;
using System.Collections.Generic;

namespace HdProject.Domain.Context.Reports
{
    // 请求 DTO
    public class BankSummaryRequestDto
    {
        public string StartDate { get; set; } = string.Empty; // yyyy-MM-dd
        public string EndDate { get; set; } = string.Empty;   // yyyy-MM-dd
        public int[]? BankSKs { get; set; }                   // 允许 null 表示全部
    }

    // 动态统一日报表请求 DTO
    public class DynamicUnifiedDailyReportRequestDto
    {
        public string BeginDate { get; set; } = string.Empty; // YYYYMMDD格式
        public string EndDate { get; set; } = string.Empty;   // YYYYMMDD格式
        public int ShopId { get; set; } = 11;                 // 门店ID，默认值11
        public string Lang { get; set; } = "EN";              // 语言参数：'EN'英文/'ZH'中文，默认'EN'
    }

    // 响应 DTO 顶层
    public class BankSummaryResponseDto
    {
        public List<ShopHeaderDto> ColumnHeaders { get; set; } = new();
        public List<BankSummaryRowDto> Rows { get; set; } = new();
        public BankSummaryGrandTotalDto GrandTotal { get; set; } = new();
    }

    // 动态列头（门店）
    public class ShopHeaderDto
    {
        public int ShopID { get; set; }
        public string ShopName { get; set; } = string.Empty;
    }

    // 行数据（按券）
    public class BankSummaryRowDto
    {
        public string DealName { get; set; } = string.Empty;
        public int DealSK { get; set; }
        public Dictionary<int, ShopCellDto> ShopData { get; set; } = new(); // key: ShopID
        public ShopTotalsDto RowTotal { get; set; } = new();
    }

    // 单元格（某券-某店）
    public class ShopCellDto
    {
        public int Count { get; set; }
        public decimal Amount { get; set; }
        public decimal Subsidy { get; set; }
        public decimal PlatformFee { get; set; }
        public decimal NetAmount { get; set; }
    }

    // 行合计
    public class ShopTotalsDto
    {
        public int Count { get; set; }
        public decimal Amount { get; set; }
        public decimal Subsidy { get; set; }
        public decimal PlatformFee { get; set; }
        public decimal NetAmount { get; set; }
    }

    // 总合计
    public class BankSummaryGrandTotalDto
    {
        public decimal TotalAmount { get; set; }
        public decimal TotalSubsidy { get; set; }
        public decimal TotalPlatformFee { get; set; }
        public decimal TotalNetAmount { get; set; }
    }

    // DAL 扁平行
    public class FlatReportDataDto
    {
        public int ShopID { get; set; }
        public string ShopName { get; set; } = string.Empty;
        public int DealSK { get; set; }
        public string DealName { get; set; } = string.Empty;
        public int TotalCount { get; set; }
        public decimal TotalAmount { get; set; }
        public decimal TotalSubsidy { get; set; }
        public decimal TotalPlatformFee { get; set; }
        public decimal TotalNetAmount { get; set; }
    }
}
