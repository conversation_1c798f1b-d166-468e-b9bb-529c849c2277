# Copilot Instructions for HdProject

## 项目架构与核心约定

- **架构分层**：本项目采用典型的 DDD 分层结构，主要包括：
  - `HdProject.Web.Api.Core`：Web API 层，负责 HTTP 路由、控制器、请求响应。
  - `HdProject.Application`：应用服务层，处理业务逻辑，调用仓储和领域服务。
  - `HdProject.Domain`：领域层，包含实体、聚合、接口、DTOs。
  - `HdProject.Infrastructure`：基础设施层，负责数据访问（SqlSugar ORM）、仓储实现。
  - `HdProject.Common`：通用 DTO、配置、工具类等。
- **前端**：`frontend/` 目录为 SPA 前端（如 Vue/React），与后端通过 RESTful API 通信。

## 主要开发流程

- **构建**：
  - 推荐命令：`dotnet build HdProject.Web.Api.Core/HdProject.Web.Api.Core.csproj -c Debug`
- **运行**：
  - 推荐命令：`dotnet run --project HdProject.Web.Api.Core/HdProject.Web.Api.Core.csproj`
- **测试**：
  - 测试项目位于 `HdProject.Tests/`，使用 xUnit/NUnit 规范。

## 代码与接口命名规范

- **API 路由**：统一采用 PascalCase，如 `/api/Auth/Login`、`/api/BankDeals/GetList`。
- **控制器**：以 `Controller` 结尾，放于对应领域子目录下。
- **DTO/参数**：属性名用 PascalCase，数组参数如 `bankSKs[]` 支持多值。
- **服务/仓储接口**：接口以 `I` 开头，服务实现以 `Service` 结尾，仓储实现以 `Repository` 结尾。

## 关键模式与约定

- **依赖注入**：所有服务、仓储通过构造函数注入。
- **ORM**：使用 SqlSugar，查询表达式需注意 `Contains`/`IN` 的 SQL 生成兼容性。
- **分页**：所有分页查询统一继承 `Pagination`，参数为 `page`、`rows`。
- **响应包装**：API 返回统一使用 `ResponseContext<T>`。
- **异常处理**：业务异常通过 `ResponseContext` 的 `code` 和 `message` 字段返回。

## 重要文件/目录

- `HdProject.Web.Api.Core/Controllers/`：API 控制器实现，路由规范参考此目录。
- `HdProject.Application/Services/`：业务服务实现，包含领域服务、仓储调用。
- `HdProject.Domain/Entities/`、`DTOs/`、`Interfaces/`：领域模型、数据传输对象、接口定义。
- `HdProject.Infrastructure/Repositorys/Imp/Repository.cs`：SqlSugar 仓储实现，分页/排序/条件查询核心逻辑。
- `docs/TECHNICAL_STACK.md`：技术栈说明。

## Git 提交流程

- 推荐格式：`[类型] 主题: 说明`，如 `feat: 新增银行交易分页接口`、`fix: 修复BankSK多值查询SQL错误`
- 每次提交需包含：变更内容、影响范围、功能描述

---

如需自动化/AI开发，请优先遵循本文件约定，遇到不明确的结构请参考对应目录下的实际实现。
