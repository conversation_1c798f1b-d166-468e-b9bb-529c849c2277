using System;
using System.Threading.Tasks;
using HdProject.Application.Services.Interfaces.Reports;
using HdProject.Domain.Context.Reports;
using HdProject.Web.Core;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;

namespace HdProject.Web.Api.Core.Controllers.Reports
{
    [Route("api/Reports")]
    [AllowAnonymous]
    public class ReportsController : ControllerApiBase
    {
        private readonly IReportService _reportService;
        public ReportsController(IReportService reportService)
        {
            _reportService = reportService;
        }

        [HttpGet("GetBankSummary")]
        public async Task<IActionResult> GetBankSummaryAsync([FromQuery] BankSummaryRequestDto request)
        {
            var result = await _reportService.GenerateBankSummaryReportAsync(request);
            return ApiData(result);
        }


        [HttpGet("ExportBankSummary")]
        public async Task<IActionResult> ExportBankSummaryAsync([FromQuery] BankSummaryRequestDto request)
        {
            var (bytes, fileName, contentType) = await _reportService.ExportBankSummaryXlsxAsync(
                request,
                titleDateRange: $"{request.StartDate}"
            );
            return File(bytes, contentType, fileName);
        }
    }
}
