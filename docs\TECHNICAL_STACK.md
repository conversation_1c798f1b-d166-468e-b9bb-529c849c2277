# 技术栈文档

本文档旨在记录 `HdProject` 项目所使用的主要技术和框架。

## 后端 (Backend)

- **核心框架**: ASP.NET Core
- **编程语言**: C#
- **数据访问 (ORM)**: [SqlSugar](https://github.com/donet5/SqlSugar) - 一个功能强大的 .NET ORM 框架。
- **认证与授权**: JWT (JSON Web Tokens) - 用于 API 的无状态身份验证。
- **日志**: NLog - 用于记录应用程序日志。
- **对象映射**: AutoMapper - 用于 DTOs 和实体之间的自动映射。

## 前端 (Frontend)

- **框架**: 项目结构表明使用了一个现代 JavaScript 框架（如 Vue.js, React, 或 Angular）进行单页面应用 (SPA) 开发。

## 数据库 (Database)

- 后端使用 SqlSugar ORM，理论上支持多种数据库，如 SQL Server, MySQL, PostgreSQL 等。具体使用的数据库类型需查看配置文件中的连接字符串。

## 版本控制 (Version Control)

- **工具**: Git

## 开发工具与环境

- **IDE**: Visual Studio (项目包含 `.sln` 文件)
- **包管理**: NuGet
