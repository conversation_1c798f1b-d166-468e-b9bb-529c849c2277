using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using HdProject.Application.Services.Interfaces.Reports;
using HdProject.Domain.Context.Reports;
using SqlSugar;

namespace HdProject.Application.Services.Reports
{
    public class BankSummaryDal : IBankSummaryDal
    {
        private readonly ISqlSugarClient _sqlSugarClient;
        private ISqlSugarClient OperateDataDb => _sqlSugarClient.AsTenant().GetConnection("OperateData");

        public BankSummaryDal(ISqlSugarClient sqlSugarClient)
        {
            _sqlSugarClient = sqlSugarClient;
        }

        public async Task<List<FlatReportDataDto>> GetFlatAsync(DateTime startDate, DateTime endDate, int[]? bankSKs)
        {
            var whereBank = bankSKs == null || bankSKs.Length == 0 ? string.Empty : " AND db.BankSK IN (@BankSKs)";

            var sql = $@"
SELECT
    ds.ShopName,
    ds.ShopId AS ShopID,
    dd.DealName,
    dd.DealSK,
    SUM(f.RedemptionCount) AS TotalCount,
    SUM(f.RedemptionAmount) AS TotalAmount,
    SUM(f.SubsidyAmount) AS TotalSubsidy,
    SUM(f.PlatformFee) AS TotalPlatformFee,
    SUM(f.NetAmount) AS TotalNetAmount
FROM dbo.Fact_Deal_Redemption f
JOIN dbo.Dim_Date dt ON f.DateSK = dt.DateSK
JOIN dbo.Dim_Shop ds ON f.ShopSK = ds.ShopSK
JOIN dbo.Dim_Bank_Deal dd ON f.DealSK = dd.DealSK
JOIN dbo.Dim_Bank db ON dd.BankSK = db.BankSK
WHERE dt.FullDate BETWEEN @StartDate AND @EndDate
{whereBank}
GROUP BY ds.ShopName, ds.ShopId, dd.DealName, dd.DealSK
ORDER BY dd.DealName, ds.ShopName;";

            var param = new List<SugarParameter>
            {
                new SugarParameter("@StartDate", startDate),
                new SugarParameter("@EndDate", endDate)
            };

            if (!string.IsNullOrEmpty(whereBank))
            {
                var inParams = new List<string>();
                for (int i = 0; i < bankSKs!.Length; i++)
                {
                    var name = "@b" + i;
                    inParams.Add(name);
                    param.Add(new SugarParameter(name, bankSKs[i]));
                }
                sql = sql.Replace("IN (@BankSKs)", $"IN ({string.Join(",", inParams)})");
            }

            var rows = await OperateDataDb.Ado.SqlQueryAsync<FlatReportDataDto>(sql, param);
            return rows;
        }
    }
}
