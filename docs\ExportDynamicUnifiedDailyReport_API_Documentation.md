# ExportDynamicUnifiedDailyReport API 接口文档

## 概述

新增的 `ExportDynamicUnifiedDailyReport` 接口用于导出营业报表数据为Excel格式，基于现有存储过程 `usp_GenerateDynamicUnifiedDailyReport` 生成动态统一日报表。

## 接口信息

- **接口路径**: `GET /api/Reports/ExportDynamicUnifiedDailyReport`
- **功能**: 导出营业报表数据为xlsx格式文件
- **认证**: 允许匿名访问 (AllowAnonymous)

## 请求参数

| 参数名 | 类型 | 必填 | 默认值 | 说明 |
|--------|------|------|--------|------|
| BeginDate | string | 是 | - | 开始日期，格式：YYYYMMDD (如：20241215) |
| EndDate | string | 是 | - | 结束日期，格式：YYYYMMDD (如：20241215) |
| ShopId | int | 否 | 11 | 门店ID |
| Lang | string | 否 | "EN" | 语言参数，"EN"英文/"ZH"中文 |

## 请求示例

```http
GET /api/Reports/ExportDynamicUnifiedDailyReport?BeginDate=20241201&EndDate=20241215&ShopId=11&Lang=ZH
```

## 响应

### 成功响应
- **状态码**: 200 OK
- **Content-Type**: application/vnd.openxmlformats-officedocument.spreadsheetml.sheet
- **响应体**: Excel文件二进制数据
- **文件名**: 营业报表_{BeginDate}_to_{EndDate}.xlsx

### 错误响应

#### 参数错误 (400)
```json
{
  "success": false,
  "message": "BeginDate 格式错误，应为 yyyyMMdd",
  "code": 400
}
```

#### 无数据 (404)
```json
{
  "success": false,
  "message": "未查询到数据",
  "code": 404
}
```

#### 服务器错误 (500)
```json
{
  "success": false,
  "message": "导出失败: 具体错误信息",
  "code": 500
}
```

## Excel文件结构

### 表头结构
Excel文件采用多层表头结构：

1. **基础信息列**:
   - 门店名称/Shop Name
   - 日期/Date
   - 星期/Weekday
   - 总营业收入/Total Revenue
   - 白天档/Day Time
   - 晚档/Night Time
   - 全天总营客批数/Total Batch Count
   - K+餐批数/K+ Meal Batch
   - 白天直落批数/Day Direct Fall
   - 20点后进场/After 8PM
   - 晚上直落批数/Night Direct Fall
   - K+餐人数/K+ Guest Count
   - 直落人数/Direct Fall Guests

2. **动态时间段列**:
   每个时间段包含以下子列：
   - K+
   - 特权预约/Special Reservation
   - 美团/Meituan
   - 抖音/Douyin
   - 房费/Room Fee
   - 上一档直落/Prev Slot Direct Fall
   - 小计/Subtotal

### 数据格式
- **日期格式**: YYYY-MM-DD
- **数值格式**: 整数显示，金额保留2位小数
- **时间段格式**: HH:MM-HH:MM (如：11:30-12:30)

## 技术实现

### 存储过程调用
```sql
EXEC dbo.usp_GenerateDynamicUnifiedDailyReport 
    @ShopId = 11,
    @BeginDate = '20241201',
    @EndDate = '20241215'
```

### 数据处理流程
1. 参数验证和日期格式转换
2. 调用存储过程获取原始数据
3. 解析动态时间段字段（正则匹配 `^(\d{2}:\d{2}-\d{2}:\d{2})_(.+)$`）
4. 重组数据结构为基础数据和时间段数据
5. 生成Excel文件（使用ClosedXML.Excel）
6. 返回文件流

### 关键特性
- 支持中英文双语列名
- 动态时间段列生成
- 复杂表头结构处理
- 日期格式自动转换
- 完善的错误处理机制

## 使用示例

### JavaScript/前端调用
```javascript
// 导出营业报表
const exportReport = async () => {
  try {
    const response = await axios.get('/api/Reports/ExportDynamicUnifiedDailyReport', {
      params: {
        BeginDate: '20241201',
        EndDate: '20241215',
        ShopId: 11,
        Lang: 'ZH'
      },
      responseType: 'blob'
    });

    // 创建下载链接
    const blob = new Blob([response.data]);
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = '营业报表_20241201_to_20241215.xlsx';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(link.href);
  } catch (error) {
    console.error('导出失败:', error);
  }
};
```

### C# 客户端调用
```csharp
using var httpClient = new HttpClient();
var url = "/api/Reports/ExportDynamicUnifiedDailyReport?BeginDate=20241201&EndDate=20241215&ShopId=11&Lang=ZH";

var response = await httpClient.GetAsync(url);
if (response.IsSuccessStatusCode)
{
    var fileBytes = await response.Content.ReadAsByteArrayAsync();
    await File.WriteAllBytesAsync("营业报表.xlsx", fileBytes);
}
```

## 注意事项

1. **日期格式**: 必须使用YYYYMMDD格式，如20241215
2. **门店ID**: 确保传入的ShopId在系统中存在
3. **数据量**: 大数据量导出可能需要较长时间，建议设置合适的超时时间
4. **权限**: 当前接口允许匿名访问，生产环境建议添加适当的权限控制
5. **缓存**: 建议对门店信息等静态数据进行缓存以提高性能

## 版本信息

- **创建时间**: 2025-08-15
- **版本**: v1.0
- **维护者**: HdProject开发团队
