# GEMINI 项目分析: HdProject

## 项目概述

该代码库包含 **HdProject** 的源代码，这是一个 .NET Core Web 应用程序。根据项目结构和依赖关系，它似乎是一个提供 RESTful API 的后端服务。该项目遵循清晰的架构模式，将领域、应用、基础设施和 Web API 层分置于独立的项目中。

该应用的核心功能包括：

*   **用户认证:** 基于 JWT 的认证，并支持令牌刷新。
*   **用户管理:** 基本的用户资料管理。
*   **数据库交互:** 项目使用 **SqlSugar** ORM 进行数据访问，并与多个数据库进行交互。

## 技术栈

*   **.NET 6:** 项目基于 .NET 6 框架构建。
*   **ASP.NET Core:** 用于构建 Web API。
*   **SqlSugar:** 一款高性能的 .NET ORM。
*   **JWT (JSON Web Tokens):** 用于保护 API 端点。
*   **NLog:** 用于记录应用程序事件。

## 构建和运行

要构建和运行此项目，您需要安装 .NET 6 SDK。

1.  **还原依赖项:**
    ```bash
    dotnet restore HdProject.sln
    ```

2.  **构建解决方案:**
    ```bash
    dotnet build HdProject.sln
    ```

3.  **运行 API:**
    应用程序的主入口点是 `HdProject.Web.Api.Core` 项目。
    ```bash
    dotnet run --project HdProject.Web.Api.Core
    ```
    API 将在 `launchSettings.json` 文件中指定的 URL 上可用 (例如, `https://localhost:5001`)。

## 开发规范

*   **清晰架构:** 代码被组织成不同的层 (领域、应用、基础设施、Web)，这有助于关注点分离和代码可维护性。
*   **仓储模式:** 数据访问通过仓储模式进行抽象，使得在不同数据源之间切换更加容易。
*   **依赖注入:** 项目广泛使用依赖注入来管理不同组件之间的依赖关系。
*   **日志记录:** 应用程序使用 NLog 进行结构化日志记录。请查看 `nlog.config` 文件以了解日志记录配置。

## 关键文件和目录

*   `HdProject.sln`: 包含所有项目的 Visual Studio 解决方案文件。
*   `global.json`: 指定要使用的 .NET SDK 版本。
*   `HdProject.Web.Api.Core/`: 主要的 Web API 项目，包含控制器和应用程序的入口点。
*   `HdProject.Application/`: 包含应用程序逻辑和服务。
*   `HdProject.Domain/`: 包含领域实体和接口。
*   `HdProject.Infrastructure/`: 使用 SqlSugar 实现数据访问层。
*   `HdProject.Web.Api.Core/appsettings.json`: 应用程序的主要配置文件，包括数据库连接字符串。
*   `.github/DEVELOPMENT_WORKFLOW.md`: 提供了此项目开发工作流程的详细指南。
*   `docs/`: 包含有关该项目的其他文档。

## 前端

`frontend` 目录存在，但它不包含可识别的项目结构 (例如, `package.json`)。这表明前端可能是：

*   在单独的代码库中开发。
*   尚未实现。
*   使用不遵循典型 JavaScript 项目结构的其他技术。

**待办事项:** 澄清前端项目的状态并相应地更新此部分。

---

**指令:** 请一直用中文回复我，utf-8