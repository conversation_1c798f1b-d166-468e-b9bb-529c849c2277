using HdProject.Domain.Interfaces;
using HdProject.Domain.Result.Page;
using Microsoft.Data.SqlClient;
using SqlSugar;
using System;
using System.Collections;
using System.Collections.Generic;
using System.Drawing.Printing;
using System.Linq;
using System.Linq.Expressions;
using System.Reflection;
using System.Text;
using System.Threading.Tasks;

namespace HdProject.Infrastructure.Repositories.Imp
{
    public class Repository<T> : IRepository<T> where T : class, new()
    {
        protected readonly ISqlSugarClient _db;

        public Repository(ISqlSugarClient db)
        {
            _db = db;

        }
        public string ConfigId { get; set; }
        private ISqlSugarClient RamDb
        {
            get
            {
                if (string.IsNullOrEmpty(ConfigId))
                {
                    return _db;
                }
                else
                {
                    return _db.AsTenant().GetConnection(ConfigId);
                }
            }
        }

        #region 查询操作

        public async Task<T> GetByIdAsync(object id)
        {
            return await RamDb.Queryable<T>().InSingleAsync(id);
        }

        public async Task<T> GetFirstAsync(Expression<Func<T, bool>> whereExpression)
        {
            return await RamDb.Queryable<T>().Where(whereExpression).FirstAsync();
        }

        public async Task<List<T>> GetListAsync()
        {
            return await RamDb.Queryable<T>().ToListAsync();
        }

        public async Task<List<T>> GetListAsync(Expression<Func<T, bool>> whereExpression)
        {
            return await RamDb.Queryable<T>().Where(whereExpression).ToListAsync();
        }

        public async Task<List<T>> GetListAsync(string sql, object parameters = null)
        {
            return await RamDb.Ado.SqlQueryAsync<T>(sql, parameters);
        }

        public async Task<List<T>> GetPageListAsync(Pagination page, Expression<Func<T, bool>> whereExpression = null)
        {
            RefAsync<int> totalCount = 0;

            // 验证排序字段是否合法（防止SQL注入）
            var entityType = typeof(T);
            var validProperties = entityType.GetProperties().Select(p => p.Name);

            if (!validProperties.Contains(page.Sidx, StringComparer.OrdinalIgnoreCase))
            {
                page.Sidx = validProperties.First(); // 如果排序的请求字段不属于实体属性字段，则根据实体类中的第一个属性字段进行默认排序
            }

            if (string.IsNullOrEmpty(page.Sord) || (page.Sord.ToLower() != "desc" && page.Sord.ToLower() != "asc"))
            {
                page.Sord = "DESC";//如果前端没有传入排序方式，或者排序方式名称错误，则默认倒序。
            }

            // 验证排序方向
            page.Sord = page.Sord.Equals("ASC", StringComparison.OrdinalIgnoreCase) ? "ASC" : "DESC";

            // 构建排序条件
            var orderBy = $"{page.Sidx} {page.Sord}";

            var query = RamDb.Queryable<T>().OrderBy(orderBy);

            if (whereExpression != null)
            {
                query = query.Where(whereExpression);
            }

            var list = await query.ToPageListAsync(page.Page, page.Rows, totalCount);
            page.Records = totalCount;
            return list;
        }
        /// <summary>
        /// 查询存储过程&分页信息
        /// </summary>
        /// <param name="page"></param>
        /// <param name="name"></param>
        /// <param name="parameters"></param>
        /// <returns></returns>
        public async Task<List<T>> GetProcedureAsync(Pagination page, string name, object parameters = null)
        {
            // 验证排序字段是否合法（防止SQL注入）
            var entityType = typeof(T);
            var validProperties = entityType.GetProperties().Select(p => p.Name);

            if (!validProperties.Contains(page.Sidx, StringComparer.OrdinalIgnoreCase))
            {
                page.Sidx = validProperties.First(); // 如果排序的请求字段不属于实体属性字段，则根据实体类中的第一个属性字段进行默认排序
            }

            if (string.IsNullOrEmpty(page.Sord) || (page.Sord.ToLower() != "desc" && page.Sord.ToLower() != "asc"))
            {
                page.Sord = "DESC";//如果前端没有传入排序方式，或者排序方式名称错误，则默认倒序。
            }

            // 验证排序方向
            page.Sord = page.Sord.Equals("ASC", StringComparison.OrdinalIgnoreCase) ? "ASC" : "DESC";

            // 获取数据
            var allData = await RamDb.Ado.UseStoredProcedure().SqlQueryAsync<T>(name, parameters);

            // 使用反射进行排序
            var propertyInfo = entityType.GetProperty(page.Sidx, BindingFlags.IgnoreCase | BindingFlags.Public | BindingFlags.Instance);

            var orderedData = page.Sord == "ASC"
                ? allData.OrderBy(x => propertyInfo.GetValue(x, null)).ToList()
                : allData.OrderByDescending(x => propertyInfo.GetValue(x, null)).ToList();

            // 分页
            var pageData = orderedData
                .Skip((page.Page - 1) * page.Rows)
                .Take(page.Rows)
                .ToList();

            page.Records = orderedData.Count;
            return pageData;
        }
        public async Task<List<T>> GetAllByProcedureAsync(string name, object parameters = null)
        {
            return await RamDb.Ado.UseStoredProcedure().SqlQueryAsync<T>(name, parameters);
        }

        public async Task<bool> ExistsAsync(Expression<Func<T, bool>> whereExpression)
        {
            return await RamDb.Queryable<T>().Where(whereExpression).AnyAsync();
        }

        public async Task<int> CountAsync(Expression<Func<T, bool>> whereExpression = null)
        {
            if (whereExpression == null)
                return await RamDb.Queryable<T>().CountAsync();

            return await RamDb.Queryable<T>().Where(whereExpression).CountAsync();
        }

        #endregion

        #region 增删改操作

        public async Task<int> InsertAsync(T entity)
        {
            return await RamDb.Insertable(entity).ExecuteCommandAsync();
        }

        public async Task<int> InsertRangeAsync(List<T> entities)
        {
            return await RamDb.Insertable(entities).ExecuteCommandAsync();
        }

        public async Task<int> UpdateAsync(T entity)
        {
            return await RamDb.Updateable(entity).ExecuteCommandAsync();
        }

        public async Task<int> UpdateAsync(Expression<Func<T, bool>> whereExpression, Expression<Func<T, T>> updateExpression)
        {
            return await RamDb.Updateable<T>()
                .SetColumns(updateExpression)
                .Where(whereExpression)
                .ExecuteCommandAsync();
        }

        public async Task<int> DeleteAsync(object id)
        {
            return await RamDb.Deleteable<T>(id).ExecuteCommandAsync();
        }

        public async Task<int> DeleteAsync(Expression<Func<T, bool>> whereExpression)
        {
            return await RamDb.Deleteable<T>().Where(whereExpression).ExecuteCommandAsync();
        }

        #endregion

        #region 事务管理

        public void BeginTran()
        {
            RamDb.Ado.BeginTran();
        }

        public void CommitTran()
        {
            RamDb.Ado.CommitTran();
        }

        public void RollbackTran()
        {
            RamDb.Ado.RollbackTran();
        }

        #endregion


    }
}